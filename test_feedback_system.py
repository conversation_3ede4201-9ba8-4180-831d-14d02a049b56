#!/usr/bin/env python
"""
Test the student feedback system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from course_matcher.models import (
    CounselorStudentAssignment, CounselorFeedback
)
from datetime import datetime, timedelta

def test_feedback_list():
    """Test the feedback list view"""
    print("=== Testing Feedback List ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test the main feedback page
    url = reverse('counselor_feedback')
    response = client.get(url)
    
    if response.status_code == 200:
        print("✓ Feedback list page loads successfully")
        
        # Test filtering
        filter_tests = [
            ('?type=academic', 'Academic type filter'),
            ('?type=career', 'Career type filter'),
            ('?priority=high', 'High priority filter'),
            ('?follow_up=required', 'Follow-up required filter'),
        ]
        
        for filter_param, description in filter_tests:
            filter_url = url + filter_param
            response = client.get(filter_url)
            
            if response.status_code == 200:
                print(f"✓ {description} works correctly")
            else:
                print(f"❌ {description} failed: {response.status_code}")
                
    else:
        print(f"❌ Feedback list page failed: {response.status_code}")
        return False
    
    return True

def test_feedback_creation():
    """Test feedback creation workflow"""
    print("\n=== Testing Feedback Creation ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Get a student to send feedback to
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if not assignment:
        print("❌ No student assignments found")
        return False
    
    student = assignment.student
    print(f"✓ Testing feedback creation for: {student.user.get_full_name()}")
    
    # Test 1: Load feedback creation page
    url = reverse('counselor_feedback_create', args=[student.id])
    response = client.get(url)
    
    if response.status_code == 200:
        print("✓ Feedback creation page loads successfully")
    else:
        print(f"❌ Feedback creation page failed: {response.status_code}")
        return False
    
    # Test 2: Submit academic feedback
    feedback_data = {
        'feedback_type': 'academic',
        'priority': 'medium',
        'title': 'Academic Performance Review',
        'content': 'I wanted to discuss your recent academic performance and provide some guidance to help you succeed in your studies. Your progress has been good overall, but there are some areas where we can focus on improvement.',
        'recommendations_text': 'Review study habits and time management\nConsider tutoring resources if needed\nMeet with professors during office hours',
        'follow_up_required': False,
    }
    
    response = client.post(url, feedback_data)
    
    if response.status_code == 302:  # Should redirect after successful submission
        print("✓ Academic feedback submitted successfully")
        
        # Check if feedback was saved
        feedback = CounselorFeedback.objects.filter(
            student=student,
            counselor__user__username='counselor',
            title='Academic Performance Review'
        ).first()
        
        if feedback:
            print("✓ Feedback saved to database")
            print(f"  - Type: {feedback.get_feedback_type_display()}")
            print(f"  - Priority: {feedback.get_priority_display()}")
            print(f"  - Recommendations count: {len(feedback.recommendations) if feedback.recommendations else 0}")
        else:
            print("❌ Feedback not saved to database")
            return False
            
    else:
        print(f"❌ Academic feedback submission failed: {response.status_code}")
        if hasattr(response, 'content'):
            print(f"   Error content: {response.content[:300]}")
        return False
    
    # Test 3: Submit feedback with follow-up
    follow_up_date = datetime.now() + timedelta(days=7)
    follow_up_data = {
        'feedback_type': 'career',
        'priority': 'high',
        'title': 'Career Development Discussion',
        'content': 'Let\'s schedule a meeting to discuss your career goals and plan your course selection for next semester.',
        'recommendations_text': 'Research internship opportunities\nUpdate your resume\nSchedule career counseling appointment',
        'follow_up_required': True,
        'follow_up_date': follow_up_date.strftime('%Y-%m-%dT%H:%M'),
    }
    
    response = client.post(url, follow_up_data)
    
    if response.status_code == 302:
        print("✓ Follow-up feedback submitted successfully")
        
        # Check if follow-up was saved
        feedback = CounselorFeedback.objects.filter(
            student=student,
            counselor__user__username='counselor',
            title='Career Development Discussion'
        ).first()
        
        if feedback and feedback.follow_up_required:
            print("✓ Follow-up feedback saved with follow-up date")
            print(f"  - Follow-up date: {feedback.follow_up_date}")
        else:
            print("❌ Follow-up feedback not saved correctly")
            
    else:
        print(f"❌ Follow-up feedback submission failed: {response.status_code}")
    
    return True

def test_feedback_templates():
    """Test feedback template functionality"""
    print("\n=== Testing Feedback Templates ===")
    
    # This would test the JavaScript template insertion functionality
    # For now, we'll just verify the templates are accessible
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if assignment:
        url = reverse('counselor_feedback_create', args=[assignment.student.id])
        response = client.get(url)
        
        # Check if template buttons are in the response
        if b'Quick Templates' in response.content:
            print("✓ Feedback templates section is present")
        else:
            print("❌ Feedback templates section not found")
            
        # Check for specific template types
        template_types = [
            b'Academic Performance',
            b'Career Guidance', 
            b'Encouragement',
            b'Academic Intervention'
        ]
        
        for template_type in template_types:
            if template_type in response.content:
                print(f"✓ {template_type.decode()} template found")
            else:
                print(f"❌ {template_type.decode()} template not found")
    
    return True

def main():
    """Run all feedback system tests"""
    print("CourseRec Feedback System Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_feedback_list()
        success &= test_feedback_creation()
        success &= test_feedback_templates()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All feedback system tests passed!")
            print("The feedback system is working correctly.")
        else:
            print("❌ Some feedback system tests failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
