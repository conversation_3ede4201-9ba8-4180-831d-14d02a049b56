{% extends 'base.html' %}

{% block title %}{{ action }} Student - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced form styling for student form */
    .form-field-group {
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-field-group:focus-within {
        transform: translateY(-2px);
    }

    .form-input {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
    }

    .form-input:focus {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 10px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .form-input:hover:not(:focus) {
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .form-input.error {
        border-color: #ef4444;
        background: rgba(254, 242, 242, 0.8);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .form-input.success {
        border-color: #10b981;
        background: rgba(240, 253, 244, 0.8);
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    .floating-label {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.9);
        padding: 0 8px;
        color: #6b7280;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        pointer-events: none;
        border-radius: 4px;
        backdrop-filter: blur(10px);
    }

    .form-input:focus + .floating-label,
    .form-input:not(:placeholder-shown) + .floating-label {
        top: 0;
        transform: translateY(-50%);
        font-size: 0.75rem;
        color: #3b82f6;
        font-weight: 600;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .section-card:hover {
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
    }

    .progress-indicator {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
        z-index: 1000;
    }

    .animate-fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    .animate-slide-up {
        animation: slideUp 0.6s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .tooltip {
        position: relative;
    }

    .tooltip::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.75rem;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .tooltip:hover::after {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(-4px);
    }

    .step-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .step {
        display: flex;
        align-items: center;
        position: relative;
    }

    .step-number {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .step.active .step-number {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .step.completed .step-number {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .step.inactive .step-number {
        background: #f3f4f6;
        color: #9ca3af;
        border: 2px solid #e5e7eb;
    }

    .step-connector {
        width: 4rem;
        height: 2px;
        background: #e5e7eb;
        margin: 0 1rem;
        transition: all 0.3s ease;
    }

    .step.completed + .step .step-connector {
        background: linear-gradient(90deg, #10b981, #3b82f6);
    }
</style>
{% endblock %}

{% block content %}
<!-- Progress Indicator -->
<div class="progress-indicator" id="formProgress"></div>

<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 animate-fade-in">
    <!-- Enhanced Header -->
    <div class="section-card rounded-xl shadow-lg p-6 mb-8 animate-slide-up">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 via-green-600 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                    </div>
                    <div class="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        {{ action }} Student
                    </h1>
                    <p class="text-gray-600 mt-1">
                        {% if action == 'Create' %}
                            Add a new student to the system with complete profile information
                        {% else %}
                            Update student information and academic details
                        {% endif %}
                    </p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Student Management
                        </span>
                        <span class="text-gray-400">•</span>
                        <span class="text-sm text-gray-500">Academic Administration</span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'management_students' %}"
                   class="inline-flex items-center px-4 py-2.5 text-gray-600 hover:text-white hover:bg-gradient-to-r hover:from-gray-600 hover:to-gray-700 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md border border-gray-300 hover:border-transparent tooltip"
                   data-tooltip="Return to student list">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Students
                </a>
            </div>
        </div>
    </div>

    <!-- Step Indicator -->
    <div class="step-indicator animate-slide-up" style="animation-delay: 0.1s;">
        <div class="step active" id="step-1">
            <div class="step-number">1</div>
            <div class="ml-2 text-sm font-medium text-gray-700">Personal Info</div>
        </div>
        <div class="step-connector"></div>
        <div class="step inactive" id="step-2">
            <div class="step-number">2</div>
            <div class="ml-2 text-sm font-medium text-gray-700">Academic Info</div>
        </div>
    </div>

    <!-- Enhanced Form Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <div class="section-card rounded-xl shadow-lg p-8 animate-slide-up" style="animation-delay: 0.2s;">
                <form method="post" class="space-y-10" id="studentForm" novalidate>
                    {% csrf_token %}

                    <!-- Personal Information Section -->
                    <div class="space-y-8" id="personal-section">
                        <div class="flex items-center space-x-4 mb-8">
                            <div class="relative">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 via-green-600 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                    </svg>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Personal Information</h3>
                                <p class="text-sm text-gray-600">Basic personal details and contact information</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- First Name Field -->
                            <div class="form-field-group">
                                <label for="first_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    First Name
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Student's legal first name">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="first_name"
                                           name="first_name"
                                           value="{% if student %}{{ student.user.first_name }}{% endif %}"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           required
                                           autocomplete="given-name">
                                    <label for="first_name" class="floating-label">Enter first name</label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="first_name-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="first_name-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Student's legal first name
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="first_name-error-msg"></div>
                            </div>
                            <!-- Last Name Field -->
                            <div class="form-field-group">
                                <label for="last_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Last Name
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Student's legal last name">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="last_name"
                                           name="last_name"
                                           value="{% if student %}{{ student.user.last_name }}{% endif %}"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           required
                                           autocomplete="family-name">
                                    <label for="last_name" class="floating-label">Enter last name</label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="last_name-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="last_name-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Student's legal last name
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="last_name-error-msg"></div>
                            </div>

                            <!-- Email Field -->
                            <div class="form-field-group">
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Email Address
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Primary email for communication and login">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="email"
                                           id="email"
                                           name="email"
                                           value="{% if student %}{{ student.user.email }}{% endif %}"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           required
                                           autocomplete="email">
                                    <label for="email" class="floating-label"><EMAIL></label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="email-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="email-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Primary email address for communication and system access
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="email-error-msg"></div>
                            </div>
                            {% if action == 'Create' %}
                            <!-- Username Field -->
                            <div class="form-field-group">
                                <label for="username" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Username
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Unique username for system login">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="username"
                                           name="username"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           required
                                           pattern="[a-zA-Z0-9_]{3,20}"
                                           title="3-20 characters, letters, numbers, and underscores only"
                                           autocomplete="username">
                                    <label for="username" class="floating-label">e.g., john_doe2024</label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="username-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="username-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    3-20 characters, letters, numbers, and underscores only
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="username-error-msg"></div>
                            </div>

                            <!-- Password Field -->
                            <div class="form-field-group">
                                <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Password
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Secure password with minimum 8 characters">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="password"
                                           id="password"
                                           name="password"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           required
                                           minlength="8"
                                           autocomplete="new-password">
                                    <label for="password" class="floating-label">Enter secure password</label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                                        <button type="button"
                                                id="togglePassword"
                                                class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200">
                                            <svg class="w-5 h-5" id="eyeIcon" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="password-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="password-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <!-- Password Strength Indicator -->
                                <div class="mt-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="flex-1 bg-gray-200 rounded-full h-2">
                                            <div id="passwordStrength" class="h-2 rounded-full transition-all duration-300" style="width: 0%;"></div>
                                        </div>
                                        <span id="passwordStrengthText" class="text-xs text-gray-500">Weak</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Minimum 8 characters with letters, numbers, and symbols
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="password-error-msg"></div>
                            </div>
                            {% endif %}
                            <!-- Phone Number Field -->
                            <div class="form-field-group">
                                <label for="phone_number" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Phone Number
                                    <span class="tooltip ml-1" data-tooltip="Contact phone number for emergencies">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="tel"
                                           id="phone_number"
                                           name="phone_number"
                                           value="{% if student %}{{ student.phone_number }}{% endif %}"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           pattern="[0-9\-\+\(\)\s]+"
                                           autocomplete="tel">
                                    <label for="phone_number" class="floating-label">+****************</label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="phone_number-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="phone_number-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Contact phone number for emergencies (optional)
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="phone_number-error-msg"></div>
                            </div>

                            <!-- Date of Birth Field -->
                            <div class="form-field-group">
                                <label for="date_of_birth" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Date of Birth
                                    <span class="tooltip ml-1" data-tooltip="Student's date of birth for age verification">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="date"
                                           id="date_of_birth"
                                           name="date_of_birth"
                                           value="{% if student %}{{ student.date_of_birth|date:'Y-m-d' }}{% endif %}"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           max="{% now 'Y-m-d' %}"
                                           autocomplete="bday">
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="date_of_birth-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="date_of_birth-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Student's date of birth for age verification (optional)
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="date_of_birth-error-msg"></div>
                            </div>
                        </div>

                        <!-- Address Field (Full Width) -->
                        <div class="md:col-span-2 form-field-group">
                            <label for="address" class="block text-sm font-semibold text-gray-700 mb-2">
                                Home Address
                                <span class="tooltip ml-1" data-tooltip="Student's residential address">
                                    <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </label>
                            <div class="relative">
                                <textarea id="address"
                                          name="address"
                                          rows="4"
                                          class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                                          placeholder="Enter complete home address including street, city, state, and postal code...">{% if student %}{{ student.address }}{% endif %}</textarea>
                                <!-- Character Counter -->
                                <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                                    <span id="address-count">0</span> / 500 characters
                                </div>
                                <!-- Validation Icons -->
                                <div class="absolute top-3 right-3 flex items-center pointer-events-none">
                                    <svg class="w-5 h-5 text-green-500 hidden" id="address-success">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <svg class="w-5 h-5 text-red-500 hidden" id="address-error">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Complete residential address including street, city, state, and postal code (optional)
                            </p>
                            <div class="text-xs text-red-600 mt-1 hidden" id="address-error-msg"></div>
                        </div>
                    </div>

                    <!-- Academic Information Section -->
                    <div class="space-y-8" id="academic-section">
                        <div class="flex items-center space-x-4 mb-8">
                            <div class="relative">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
                                    </svg>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Academic Information</h3>
                                <p class="text-sm text-gray-600">Academic details and course preferences</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Student ID Field -->
                            <div class="form-field-group">
                                <label for="student_id" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Student ID
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Unique institutional student identifier">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="student_id"
                                           name="student_id"
                                           value="{% if student %}{{ student.student_id }}{% endif %}"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder=" "
                                           required
                                           pattern="[A-Z0-9]{6,12}"
                                           title="6-12 characters, letters and numbers only">
                                    <label for="student_id" class="floating-label">e.g., STU2024001</label>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="student_id-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="student_id-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Unique institutional student identification number
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="student_id-error-msg"></div>
                            </div>
                            <!-- Academic Year Field -->
                            <div class="form-field-group">
                                <label for="year" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Academic Year
                                    <span class="text-red-500">*</span>
                                    <span class="tooltip ml-1" data-tooltip="Current academic year level">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <select id="year"
                                            name="year"
                                            class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none"
                                            required>
                                        <option value="">Choose academic year...</option>
                                        <option value="freshman" {% if student and student.year == 'freshman' %}selected{% endif %}>
                                            🎓 Freshman (1st Year)
                                        </option>
                                        <option value="sophomore" {% if student and student.year == 'sophomore' %}selected{% endif %}>
                                            📚 Sophomore (2nd Year)
                                        </option>
                                        <option value="junior" {% if student and student.year == 'junior' %}selected{% endif %}>
                                            📖 Junior (3rd Year)
                                        </option>
                                        <option value="senior" {% if student and student.year == 'senior' %}selected{% endif %}>
                                            🎯 Senior (4th Year)
                                        </option>
                                        <option value="graduate" {% if student and student.year == 'graduate' %}selected{% endif %}>
                                            🎓 Graduate Student
                                        </option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="year-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="year-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Current academic year level for course recommendations
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="year-error-msg"></div>
                            </div>

                            <!-- Major Field -->
                            <div class="form-field-group">
                                <label for="major" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Academic Major
                                    <span class="tooltip ml-1" data-tooltip="Student's field of study">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <select id="major"
                                            name="major"
                                            class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none">
                                        <option value="">Select major (optional)...</option>
                                        {% for department in departments %}
                                        <option value="{{ department.id }}" {% if student and student.major == department %}selected{% endif %}>
                                            {{ department.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="major-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="major-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Student's primary field of study (optional)
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="major-error-msg"></div>
                            </div>
                            <!-- Expected Graduation Year Field -->
                            <div class="form-field-group">
                                <label for="expected_graduation_year" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Expected Graduation Year
                                    <span class="tooltip ml-1" data-tooltip="Anticipated year of graduation">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <input type="number"
                                           id="expected_graduation_year"
                                           name="expected_graduation_year"
                                           value="{% if student %}{{ student.expected_graduation_year }}{% endif %}"
                                           min="2024"
                                           max="2040"
                                           class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="expected_graduation_year-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="expected_graduation_year-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Anticipated year of graduation (optional)
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="expected_graduation_year-error-msg"></div>
                            </div>

                            <!-- Preferred Difficulty Field -->
                            <div class="form-field-group">
                                <label for="preferred_difficulty" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Preferred Difficulty Level
                                    <span class="tooltip ml-1" data-tooltip="Preferred course difficulty for recommendations">
                                        <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="relative">
                                    <select id="preferred_difficulty"
                                            name="preferred_difficulty"
                                            class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none">
                                        <option value="beginner" {% if student and student.preferred_difficulty == 'beginner' %}selected{% endif %}>
                                            🌱 Beginner - New to the subject
                                        </option>
                                        <option value="intermediate" {% if student and student.preferred_difficulty == 'intermediate' %}selected{% endif %}>
                                            📈 Intermediate - Some experience
                                        </option>
                                        <option value="advanced" {% if student and student.preferred_difficulty == 'advanced' %}selected{% endif %}>
                                            🚀 Advanced - Challenging courses
                                        </option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <!-- Validation Icons -->
                                    <div class="absolute inset-y-0 right-12 flex items-center pointer-events-none">
                                        <svg class="w-5 h-5 text-green-500 hidden" id="preferred_difficulty-success">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <svg class="w-5 h-5 text-red-500 hidden" id="preferred_difficulty-error">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Preferred course difficulty level for personalized recommendations
                                </p>
                                <div class="text-xs text-red-600 mt-1 hidden" id="preferred_difficulty-error-msg"></div>
                            </div>
                        </div>

                        <!-- Career Goals Field (Full Width) -->
                        <div class="md:col-span-2 form-field-group">
                            <label for="career_goals" class="block text-sm font-semibold text-gray-700 mb-2">
                                Career Goals & Aspirations
                                <span class="tooltip ml-1" data-tooltip="Student's career objectives and professional goals">
                                    <svg class="w-4 h-4 text-gray-400 inline" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </label>
                            <div class="relative">
                                <textarea id="career_goals"
                                          name="career_goals"
                                          rows="5"
                                          class="form-input w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                                          placeholder="Describe your career aspirations, professional goals, and areas of interest:
• What industry or field interests you?
• What type of role do you envision?
• What skills do you want to develop?
• Any specific companies or organizations you're targeting?">{% if student %}{{ student.career_goals }}{% endif %}</textarea>
                                <!-- Character Counter -->
                                <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                                    <span id="career_goals-count">0</span> / 1000 characters
                                </div>
                                <!-- Validation Icons -->
                                <div class="absolute top-3 right-3 flex items-center pointer-events-none">
                                    <svg class="w-5 h-5 text-green-500 hidden" id="career_goals-success">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <svg class="w-5 h-5 text-red-500 hidden" id="career_goals-error">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Career aspirations and professional goals to help tailor course recommendations (optional)
                            </p>
                            <div class="text-xs text-red-600 mt-1 hidden" id="career_goals-error-msg"></div>
                        </div>
                    </div>

                    <!-- Enhanced Form Actions -->
                    <div class="flex flex-col sm:flex-row items-center justify-between gap-4 pt-10 border-t border-gray-200/50">
                        <div class="flex items-center space-x-4">
                            <a href="{% url 'management_students' %}"
                               class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </a>
                            <button type="button"
                                    id="saveAsDraft"
                                    class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                Save as Draft
                            </button>
                        </div>

                        <button type="submit"
                                id="submitBtn"
                                class="inline-flex items-center px-8 py-3.5 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-green-500 via-green-600 to-emerald-700 hover:from-green-600 hover:via-green-700 hover:to-emerald-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span class="flex items-center">
                                {% if action == 'Create' %}
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create Student
                                {% else %}
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Update Student
                                {% endif %}
                            </span>
                            <!-- Loading Spinner -->
                            <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white hidden" id="submitSpinner" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                    </div>
            </form>
        </div>
    </div>

        <!-- Enhanced Help Sidebar -->
        <div class="lg:col-span-1">
            <div class="section-card rounded-xl shadow-lg p-6 sticky top-6 animate-slide-up" style="animation-delay: 0.4s;">
                <h3 class="text-lg font-bold text-gray-900 mb-6 flex items-center">
                    <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-2">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    Student Guidelines
                </h3>
                <div class="space-y-6 text-sm text-gray-600">
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-green-800 mb-1">Personal Information</div>
                                <div class="text-green-700">Ensure all personal details are accurate for proper identification and communication.</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-blue-800 mb-1">Student ID</div>
                                <div class="text-blue-700">Use a unique identifier that follows your institution's numbering system.</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-purple-800 mb-1">Academic Year</div>
                                <div class="text-purple-700">Select the appropriate year level to help with course recommendations and academic planning.</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-orange-800 mb-1">Difficulty Preference</div>
                                <div class="text-orange-700">Choose the preferred difficulty level to optimize course recommendations for the student.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('studentForm');
    const progressBar = document.getElementById('formProgress');
    const submitBtn = document.getElementById('submitBtn');
    const submitSpinner = document.getElementById('submitSpinner');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const passwordStrengthBar = document.getElementById('passwordStrength');
    const passwordStrengthText = document.getElementById('passwordStrengthText');
    const addressTextarea = document.getElementById('address');
    const addressCounter = document.getElementById('address-count');
    const careerGoalsTextarea = document.getElementById('career_goals');
    const careerGoalsCounter = document.getElementById('career_goals-count');

    // Step indicators
    const step1 = document.getElementById('step-1');
    const step2 = document.getElementById('step-2');

    // Form validation rules
    const validationRules = {
        first_name: {
            required: true,
            minLength: 2,
            maxLength: 50,
            pattern: /^[a-zA-Z\s\-\']+$/
        },
        last_name: {
            required: true,
            minLength: 2,
            maxLength: 50,
            pattern: /^[a-zA-Z\s\-\']+$/
        },
        email: {
            required: true,
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        },
        username: {
            required: true,
            minLength: 3,
            maxLength: 20,
            pattern: /^[a-zA-Z0-9_]+$/,
            transform: (value) => value.toLowerCase()
        },
        password: {
            required: true,
            minLength: 8,
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
        },
        phone_number: {
            required: false,
            pattern: /^[\+]?[1-9][\d]{0,15}$/
        },
        student_id: {
            required: true,
            pattern: /^[A-Z0-9]{6,12}$/,
            transform: (value) => value.toUpperCase()
        },
        year: {
            required: true
        },
        expected_graduation_year: {
            required: false,
            min: 2024,
            max: 2040
        }
    };

    // Real-time validation
    function validateField(fieldName, value) {
        const rules = validationRules[fieldName];
        if (!rules) return { valid: true };

        const errors = [];

        if (rules.required && (!value || value.trim() === '')) {
            errors.push('This field is required');
        }

        if (value && rules.minLength && value.length < rules.minLength) {
            errors.push(`Minimum ${rules.minLength} characters required`);
        }

        if (value && rules.maxLength && value.length > rules.maxLength) {
            errors.push(`Maximum ${rules.maxLength} characters allowed`);
        }

        if (value && rules.min && parseFloat(value) < rules.min) {
            errors.push(`Minimum value is ${rules.min}`);
        }

        if (value && rules.max && parseFloat(value) > rules.max) {
            errors.push(`Maximum value is ${rules.max}`);
        }

        if (value && rules.pattern && !rules.pattern.test(value)) {
            if (fieldName === 'email') {
                errors.push('Please enter a valid email address');
            } else if (fieldName === 'username') {
                errors.push('Only letters, numbers, and underscores allowed');
            } else if (fieldName === 'password') {
                errors.push('Password must contain uppercase, lowercase, number, and special character');
            } else if (fieldName === 'student_id') {
                errors.push('Format: 6-12 characters, letters and numbers only');
            } else if (fieldName === 'phone_number') {
                errors.push('Please enter a valid phone number');
            } else if (fieldName === 'first_name' || fieldName === 'last_name') {
                errors.push('Only letters, spaces, hyphens, and apostrophes allowed');
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    // Update field validation UI
    function updateFieldValidation(fieldName, isValid, errors = []) {
        const field = document.getElementById(fieldName);
        const successIcon = document.getElementById(`${fieldName}-success`);
        const errorIcon = document.getElementById(`${fieldName}-error`);
        const errorMsg = document.getElementById(`${fieldName}-error-msg`);

        if (!field) return;

        // Reset classes
        field.classList.remove('error', 'success');

        if (successIcon) successIcon.classList.add('hidden');
        if (errorIcon) errorIcon.classList.add('hidden');
        if (errorMsg) errorMsg.classList.add('hidden');

        if (field.value.trim() !== '') {
            if (isValid) {
                field.classList.add('success');
                if (successIcon) successIcon.classList.remove('hidden');
            } else {
                field.classList.add('error');
                if (errorIcon) errorIcon.classList.remove('hidden');
                if (errorMsg && errors.length > 0) {
                    errorMsg.textContent = errors[0];
                    errorMsg.classList.remove('hidden');
                }
            }
        }
    }

    // Password strength checker
    function checkPasswordStrength(password) {
        let strength = 0;
        let feedback = 'Weak';
        let color = '#ef4444';

        if (password.length >= 8) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;

        switch (strength) {
            case 0:
            case 1:
                feedback = 'Very Weak';
                color = '#ef4444';
                break;
            case 2:
                feedback = 'Weak';
                color = '#f59e0b';
                break;
            case 3:
                feedback = 'Fair';
                color = '#eab308';
                break;
            case 4:
                feedback = 'Good';
                color = '#22c55e';
                break;
            case 5:
                feedback = 'Strong';
                color = '#16a34a';
                break;
        }

        return {
            strength: (strength / 5) * 100,
            feedback: feedback,
            color: color
        };
    }

    // Character counters
    function updateCharacterCounter(textarea, counter, maxLength) {
        if (!textarea || !counter) return;

        const count = textarea.value.length;
        counter.textContent = count;

        if (count > maxLength) {
            counter.classList.add('text-red-500');
            counter.classList.remove('text-gray-400', 'text-yellow-500');
        } else if (count > maxLength * 0.9) {
            counter.classList.add('text-yellow-500');
            counter.classList.remove('text-gray-400', 'text-red-500');
        } else {
            counter.classList.add('text-gray-400');
            counter.classList.remove('text-yellow-500', 'text-red-500');
        }
    }

    // Progress indicator
    function updateProgress() {
        const requiredFields = ['first_name', 'last_name', 'email', 'student_id', 'year'];
        if (document.getElementById('username')) {
            requiredFields.push('username', 'password');
        }

        let validFields = 0;

        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (field && field.value.trim() !== '') {
                const validation = validateField(fieldName, field.value);
                if (validation.valid) {
                    validFields++;
                }
            }
        });

        const progress = (validFields / requiredFields.length) * 100;
        progressBar.style.transform = `scaleX(${progress / 100})`;

        // Update step indicators
        if (progress >= 50) {
            step1.classList.remove('active');
            step1.classList.add('completed');
            step2.classList.remove('inactive');
            step2.classList.add('active');
        } else {
            step1.classList.add('active');
            step1.classList.remove('completed');
            step2.classList.add('inactive');
            step2.classList.remove('active');
        }
    }

    // Password toggle functionality
    if (togglePasswordBtn && passwordField) {
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            const eyeIcon = document.getElementById('eyeIcon');
            if (type === 'text') {
                eyeIcon.innerHTML = '<path d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l-1.415-1.414m4.243 4.243l1.414 1.414M14.121 14.121L15.536 15.536m-1.415-1.415l1.415 1.415"></path>';
            } else {
                eyeIcon.innerHTML = '<path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>';
            }
        });
    }

    // Add event listeners
    Object.keys(validationRules).forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('input', function() {
                let value = this.value;

                // Apply transformations
                const rules = validationRules[fieldName];
                if (rules.transform) {
                    value = rules.transform(value);
                    this.value = value;
                }

                const validation = validateField(fieldName, value);
                updateFieldValidation(fieldName, validation.valid, validation.errors);

                // Special handling for password strength
                if (fieldName === 'password' && passwordStrengthBar && passwordStrengthText) {
                    const strength = checkPasswordStrength(value);
                    passwordStrengthBar.style.width = strength.strength + '%';
                    passwordStrengthBar.style.backgroundColor = strength.color;
                    passwordStrengthText.textContent = strength.feedback;
                    passwordStrengthText.style.color = strength.color;
                }

                updateProgress();
            });

            field.addEventListener('blur', function() {
                const validation = validateField(fieldName, this.value);
                updateFieldValidation(fieldName, validation.valid, validation.errors);
            });
        }
    });

    // Character counters
    if (addressTextarea && addressCounter) {
        addressTextarea.addEventListener('input', () => updateCharacterCounter(addressTextarea, addressCounter, 500));
        updateCharacterCounter(addressTextarea, addressCounter, 500);
    }

    if (careerGoalsTextarea && careerGoalsCounter) {
        careerGoalsTextarea.addEventListener('input', () => updateCharacterCounter(careerGoalsTextarea, careerGoalsCounter, 1000));
        updateCharacterCounter(careerGoalsTextarea, careerGoalsCounter, 1000);
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all required fields
        const requiredFields = ['first_name', 'last_name', 'email', 'student_id', 'year'];
        if (document.getElementById('username')) {
            requiredFields.push('username', 'password');
        }

        let isFormValid = true;
        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (field) {
                const validation = validateField(fieldName, field.value);
                updateFieldValidation(fieldName, validation.valid, validation.errors);
                if (!validation.valid) {
                    isFormValid = false;
                }
            }
        });

        if (!isFormValid) {
            // Scroll to first error
            const firstError = document.querySelector('.form-input.error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitSpinner.classList.remove('hidden');
        submitBtn.querySelector('span').style.opacity = '0.7';

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Save as draft functionality
    const saveAsDraftBtn = document.getElementById('saveAsDraft');
    if (saveAsDraftBtn) {
        saveAsDraftBtn.addEventListener('click', function() {
            // Add draft field
            const draftInput = document.createElement('input');
            draftInput.type = 'hidden';
            draftInput.name = 'save_as_draft';
            draftInput.value = 'true';
            form.appendChild(draftInput);

            // Submit form
            form.submit();
        });
    }

    // Initialize progress
    updateProgress();

    // Auto-save functionality (optional)
    let autoSaveTimeout;
    function autoSave() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(() => {
            const formData = new FormData(form);
            // Implement auto-save logic here if needed
            console.log('Auto-saving form data...');
        }, 30000); // Auto-save every 30 seconds
    }

    // Trigger auto-save on form changes
    form.addEventListener('input', autoSave);
});
</script>
{% endblock %}
