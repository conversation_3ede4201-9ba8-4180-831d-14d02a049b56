{% extends 'base.html' %}

{% block title %}Student Feedback - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Student Feedback Management</h1>
            <p class="text-gray-600">Provide guidance and track communication with students</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Filters -->
            <div class="flex space-x-2">
                <select id="typeFilter" class="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="all" {% if feedback_type_filter == 'all' %}selected{% endif %}>All Types</option>
                    {% for value, label in feedback_types %}
                        <option value="{{ value }}" {% if feedback_type_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>

                <select id="priorityFilter" class="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="all" {% if priority_filter == 'all' %}selected{% endif %}>All Priorities</option>
                    {% for value, label in priority_levels %}
                        <option value="{{ value }}" {% if priority_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>

                <select id="followUpFilter" class="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="all" {% if follow_up_filter == 'all' %}selected{% endif %}>All Follow-ups</option>
                    <option value="required" {% if follow_up_filter == 'required' %}selected{% endif %}>Follow-up Required</option>
                    <option value="overdue" {% if follow_up_filter == 'overdue' %}selected{% endif %}>Overdue</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ total_feedback }}</div>
            <div class="text-sm text-gray-600">Total Feedback</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ pending_follow_ups }}</div>
            <div class="text-sm text-gray-600">Pending Follow-ups</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-red-600">{{ overdue_follow_ups }}</div>
            <div class="text-sm text-gray-600">Overdue Follow-ups</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ feedback_list|length }}</div>
            <div class="text-sm text-gray-600">Filtered Results</div>
        </div>
    </div>
</div>

<!-- Feedback List -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
    {% if feedback_list %}
        <div class="divide-y divide-gray-200">
            {% for feedback in feedback_list %}
            <div class="p-6 hover:bg-gray-50/50 transition-colors">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <!-- Student Info -->
                        <div class="flex items-center space-x-4 mb-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                {{ feedback.student.user.first_name|slice:":1" }}{{ feedback.student.user.last_name|slice:":1" }}
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ feedback.student.user.get_full_name }}</h3>
                                <p class="text-sm text-gray-600">{{ feedback.student.major.name|default:"No major selected" }}</p>
                            </div>
                        </div>

                        <!-- Feedback Content -->
                        <div class="mb-4">
                            <div class="flex items-center space-x-3 mb-2">
                                <h4 class="text-xl font-semibold text-gray-900">{{ feedback.title }}</h4>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if feedback.feedback_type == 'general' %}bg-gray-100 text-gray-800
                                    {% elif feedback.feedback_type == 'academic' %}bg-blue-100 text-blue-800
                                    {% elif feedback.feedback_type == 'career' %}bg-green-100 text-green-800
                                    {% elif feedback.feedback_type == 'course_recommendation' %}bg-purple-100 text-purple-800
                                    {% elif feedback.feedback_type == 'intervention' %}bg-red-100 text-red-800
                                    {% endif %}">
                                    {{ feedback.get_feedback_type_display }}
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if feedback.priority == 'low' %}bg-gray-100 text-gray-800
                                    {% elif feedback.priority == 'medium' %}bg-yellow-100 text-yellow-800
                                    {% elif feedback.priority == 'high' %}bg-orange-100 text-orange-800
                                    {% elif feedback.priority == 'urgent' %}bg-red-100 text-red-800
                                    {% endif %}">
                                    {{ feedback.get_priority_display }}
                                </span>
                            </div>
                            <p class="text-gray-700 mb-3">{{ feedback.content|truncatewords:30 }}</p>

                            <!-- Recommendations -->
                            {% if feedback.recommendations %}
                            <div class="bg-blue-50 rounded-lg p-3 mb-3">
                                <h5 class="font-semibold text-blue-900 mb-2">Recommendations:</h5>
                                <ul class="list-disc list-inside space-y-1">
                                    {% for recommendation in feedback.recommendations %}
                                        <li class="text-sm text-blue-800">{{ recommendation }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Metadata -->
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <div class="flex items-center space-x-4">
                                <span>{{ feedback.created_at|date:"M d, Y" }} at {{ feedback.created_at|time:"g:i A" }}</span>
                                {% if feedback.follow_up_required %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if feedback.follow_up_date < now %}bg-red-100 text-red-800
                                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                        </svg>
                                        Follow-up: {{ feedback.follow_up_date|date:"M d" }}
                                        {% if feedback.follow_up_date < now %}(Overdue){% endif %}
                                    </span>
                                {% endif %}
                                {% if not feedback.is_read %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Unread by Student
                                    </span>
                                {% endif %}
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'counselor_student_detail' feedback.student.id %}"
                                   class="text-purple-600 hover:text-purple-900 font-medium">View Student</a>
                                <button onclick="viewFeedbackDetail({{ feedback.id }})"
                                        class="text-blue-600 hover:text-blue-900 font-medium">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination would go here if needed -->

    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.544-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No feedback found</h3>
            <p class="mt-1 text-sm text-gray-500">
                {% if feedback_type_filter != 'all' or priority_filter != 'all' or follow_up_filter != 'all' %}
                    No feedback matches the current filters.
                {% else %}
                    You haven't sent any feedback yet.
                {% endif %}
            </p>
            <div class="mt-6">
                <a href="{% url 'counselor_students' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                    Send Feedback to Students
                </a>
            </div>
        </div>
    {% endif %}
</div>

<script>
// Filter functionality
function updateFilters() {
    const typeFilter = document.getElementById('typeFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const followUpFilter = document.getElementById('followUpFilter').value;

    const url = new URL(window.location);

    if (typeFilter === 'all') {
        url.searchParams.delete('type');
    } else {
        url.searchParams.set('type', typeFilter);
    }

    if (priorityFilter === 'all') {
        url.searchParams.delete('priority');
    } else {
        url.searchParams.set('priority', priorityFilter);
    }

    if (followUpFilter === 'all') {
        url.searchParams.delete('follow_up');
    } else {
        url.searchParams.set('follow_up', followUpFilter);
    }

    window.location.href = url.toString();
}

document.getElementById('typeFilter').addEventListener('change', updateFilters);
document.getElementById('priorityFilter').addEventListener('change', updateFilters);
document.getElementById('followUpFilter').addEventListener('change', updateFilters);

// Feedback detail modal (placeholder)
function viewFeedbackDetail(feedbackId) {
    // This would open a modal with full feedback details
    alert('Feedback detail view would open here for feedback ID: ' + feedbackId);
}
</script>
{% endblock %}
