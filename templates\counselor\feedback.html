{% extends 'base.html' %}

{% block title %}Student Feedback - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Student Feedback Management</h1>
            <p class="text-gray-600">Provide guidance and track communication with students</p>
        </div>
    </div>
</div>

<!-- Coming Soon Message -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-12 text-center">
    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
        </svg>
    </div>
    <h2 class="text-2xl font-bold text-gray-900 mb-4">Student Feedback System</h2>
    <p class="text-lg text-gray-600 mb-6">This feature will allow you to:</p>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        <div class="bg-blue-50 p-6 rounded-lg">
            <h3 class="font-semibold text-blue-900 mb-2">Send Personalized Feedback</h3>
            <p class="text-blue-700">Create targeted guidance messages for individual students</p>
        </div>
        <div class="bg-green-50 p-6 rounded-lg">
            <h3 class="font-semibold text-green-900 mb-2">Track Communication</h3>
            <p class="text-green-700">Monitor all feedback sent and student responses</p>
        </div>
        <div class="bg-purple-50 p-6 rounded-lg">
            <h3 class="font-semibold text-purple-900 mb-2">Set Follow-up Reminders</h3>
            <p class="text-purple-700">Schedule follow-up actions and track progress</p>
        </div>
        <div class="bg-orange-50 p-6 rounded-lg">
            <h3 class="font-semibold text-orange-900 mb-2">Categorize Feedback</h3>
            <p class="text-orange-700">Organize feedback by type: academic, career, personal development</p>
        </div>
    </div>
    <div class="mt-8">
        <a href="{% url 'counselor_dashboard' %}" 
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
            Return to Dashboard
        </a>
    </div>
</div>
{% endblock %}
