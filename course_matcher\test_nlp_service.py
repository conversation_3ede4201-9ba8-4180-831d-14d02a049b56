"""
Tests for NLP Service functionality
"""
import os
import json
import unittest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, override_settings
from django.core.cache import cache
from django.contrib.auth.models import User

from .models import StudentProfile, Course, Department, AcademicRecord
from .nlp_service import GeminiNLPService, SemanticSimilarity, ContentAnalysis
from .recommendation_service import RecommendationEngine


class TestGeminiNLPService(TestCase):
    """Test cases for GeminiNLPService"""
    
    def setUp(self):
        """Set up test data"""
        cache.clear()
        
        # Mock environment variable
        self.mock_api_key = "test_api_key"
        
        # Create test data
        self.department = Department.objects.create(
            name="Computer Science",
            code="CS",
            description="Computer Science Department"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.student = StudentProfile.objects.create(
            user=self.user,
            student_id="CS001",
            year="junior",
            major=self.department,
            interests=["Machine Learning", "Web Development", "Data Science"],
            career_goals="I want to become a software engineer specializing in AI and machine learning.",
            preferred_difficulty="intermediate"
        )
        
        self.course = Course.objects.create(
            name="Introduction to Machine Learning",
            code="CS301",
            department=self.department,
            credits=3,
            description="This course introduces fundamental concepts of machine learning including supervised and unsupervised learning algorithms, neural networks, and practical applications in data science.",
            difficulty="intermediate",
            topics=["Machine Learning", "Neural Networks", "Data Science", "Python Programming"]
        )
    
    @patch.dict(os.environ, {'GEMINI_API_KEY': 'test_api_key'})
    @patch('course_matcher.nlp_service.genai')
    def test_nlp_service_initialization(self, mock_genai):
        """Test NLP service initialization"""
        mock_model = Mock()
        mock_genai.GenerativeModel.return_value = mock_model
        
        service = GeminiNLPService()
        
        self.assertIsNotNone(service.api_key)
        self.assertEqual(service.api_key, 'test_api_key')
        self.assertIsNotNone(service.model)
        mock_genai.configure.assert_called_once_with(api_key='test_api_key')
    
    @patch.dict(os.environ, {'GEMINI_API_KEY': 'test_api_key'})
    @patch('course_matcher.nlp_service.genai')
    def test_semantic_similarity_analysis(self, mock_genai):
        """Test semantic similarity analysis"""
        # Mock Gemini response
        mock_response = Mock()
        mock_response.text = '''
        {
            "score": 0.85,
            "explanation": "Strong alignment between machine learning interests and course content",
            "key_matches": ["Machine Learning", "Data Science", "Neural Networks"]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        service = GeminiNLPService()
        
        result = service.analyze_semantic_similarity(
            "Machine Learning, Data Science",
            "Introduction to machine learning algorithms and neural networks",
            "academic course matching"
        )
        
        self.assertIsInstance(result, SemanticSimilarity)
        self.assertEqual(result.score, 0.85)
        self.assertIn("Strong alignment", result.explanation)
        self.assertIn("Machine Learning", result.key_matches)
    
    @patch.dict(os.environ, {'GEMINI_API_KEY': 'test_api_key'})
    @patch('course_matcher.nlp_service.genai')
    def test_course_content_analysis(self, mock_genai):
        """Test course content analysis"""
        mock_response = Mock()
        mock_response.text = '''
        {
            "topics": ["Machine Learning", "Neural Networks", "Data Analysis", "Python"],
            "difficulty_assessment": "intermediate",
            "learning_outcomes": ["Understand ML algorithms", "Implement neural networks", "Analyze data"],
            "prerequisites_suggested": ["Statistics", "Programming Fundamentals"]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        service = GeminiNLPService()
        
        result = service.analyze_course_content(
            self.course.description,
            self.course.name,
            self.course.topics
        )
        
        self.assertIsInstance(result, ContentAnalysis)
        self.assertEqual(result.difficulty_assessment, "intermediate")
        self.assertIn("Machine Learning", result.topics)
        self.assertIn("Statistics", result.prerequisites_suggested)
    
    @patch.dict(os.environ, {'GEMINI_API_KEY': 'test_api_key'})
    @patch('course_matcher.nlp_service.genai')
    def test_recommendation_explanation_generation(self, mock_genai):
        """Test enhanced recommendation explanation generation"""
        mock_response = Mock()
        mock_response.text = "This course perfectly aligns with your machine learning interests and will provide essential skills for your AI career goals. The hands-on approach to neural networks and data science applications makes it an ideal next step in your academic journey."
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        service = GeminiNLPService()
        
        result = service.generate_recommendation_explanation(
            student_interests=self.student.interests,
            student_goals=self.student.career_goals,
            course_name=self.course.name,
            course_description=self.course.description,
            similarity_score=0.85,
            traditional_reasoning="This course matches your academic level and major requirements."
        )
        
        self.assertIsInstance(result, str)
        self.assertIn("machine learning", result.lower())
        self.assertIn("career", result.lower())
    
    @patch.dict(os.environ, {'GEMINI_API_KEY': 'test_api_key'})
    @patch('course_matcher.nlp_service.genai')
    def test_caching_functionality(self, mock_genai):
        """Test that results are properly cached"""
        mock_response = Mock()
        mock_response.text = '''
        {
            "score": 0.75,
            "explanation": "Good alignment",
            "key_matches": ["Programming", "Data"]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        service = GeminiNLPService()
        
        # First call should hit the API
        result1 = service.analyze_semantic_similarity("test1", "test2", "context")
        
        # Second call with same parameters should use cache
        result2 = service.analyze_semantic_similarity("test1", "test2", "context")
        
        # Should only call the API once due to caching
        self.assertEqual(mock_model.generate_content.call_count, 1)
        self.assertEqual(result1.score, result2.score)
    
    @patch.dict(os.environ, {'GEMINI_API_KEY': 'test_api_key'})
    @patch('course_matcher.nlp_service.genai')
    def test_error_handling(self, mock_genai):
        """Test error handling when API fails"""
        mock_model = Mock()
        mock_model.generate_content.side_effect = Exception("API Error")
        mock_genai.GenerativeModel.return_value = mock_model
        
        service = GeminiNLPService()
        
        # Should return fallback result instead of raising exception
        result = service.analyze_semantic_similarity("test1", "test2", "context")
        
        self.assertIsInstance(result, SemanticSimilarity)
        self.assertEqual(result.score, 0.3)  # Default fallback score
        self.assertIn("Unable to analyze", result.explanation)
    
    def test_missing_api_key(self):
        """Test initialization fails gracefully without API key"""
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError):
                GeminiNLPService()


class TestEnhancedRecommendationEngine(TestCase):
    """Test cases for enhanced recommendation engine with NLP"""
    
    def setUp(self):
        """Set up test data"""
        cache.clear()
        
        # Create test data (same as above)
        self.department = Department.objects.create(
            name="Computer Science",
            code="CS",
            description="Computer Science Department"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        self.student = StudentProfile.objects.create(
            user=self.user,
            student_id="CS001",
            year="junior",
            major=self.department,
            interests=["Machine Learning", "Web Development"],
            career_goals="Software engineer specializing in AI",
            preferred_difficulty="intermediate"
        )
        
        self.course = Course.objects.create(
            name="Machine Learning Fundamentals",
            code="CS301",
            department=self.department,
            credits=3,
            description="Comprehensive introduction to machine learning algorithms and applications",
            difficulty="intermediate",
            topics=["Machine Learning", "Algorithms", "Data Science"]
        )
    
    @patch('course_matcher.recommendation_service.NLP_AVAILABLE', True)
    @patch('course_matcher.recommendation_service.GeminiNLPService')
    def test_nlp_enhanced_scoring(self, mock_nlp_service_class):
        """Test NLP-enhanced scoring in recommendation engine"""
        # Mock NLP service
        mock_nlp_service = Mock()
        mock_nlp_service.compute_enhanced_similarity.return_value = 0.8
        mock_nlp_service_class.return_value = mock_nlp_service
        
        engine = RecommendationEngine()
        
        # Test NLP scoring
        nlp_score = engine._nlp_enhanced_score(self.student, self.course)
        
        self.assertEqual(nlp_score, 0.8)
        mock_nlp_service.compute_enhanced_similarity.assert_called_once()
    
    @patch('course_matcher.recommendation_service.NLP_AVAILABLE', False)
    def test_fallback_when_nlp_unavailable(self):
        """Test fallback behavior when NLP is unavailable"""
        engine = RecommendationEngine()
        
        # Should return default score when NLP is unavailable
        nlp_score = engine._nlp_enhanced_score(self.student, self.course)
        
        self.assertEqual(nlp_score, 0.5)  # Default fallback score
    
    @patch('course_matcher.recommendation_service.NLP_AVAILABLE', True)
    @patch('course_matcher.recommendation_service.GeminiNLPService')
    def test_enhanced_reasoning_generation(self, mock_nlp_service_class):
        """Test enhanced reasoning generation"""
        # Mock NLP service
        mock_nlp_service = Mock()
        mock_nlp_service.generate_recommendation_explanation.return_value = "AI-enhanced explanation"
        mock_nlp_service_class.return_value = mock_nlp_service
        
        engine = RecommendationEngine()
        
        reasoning = engine._generate_enhanced_reasoning(
            self.student, self.course, 0.7, 0.8, 0.6, 0.9
        )
        
        self.assertEqual(reasoning, "AI-enhanced explanation")
        mock_nlp_service.generate_recommendation_explanation.assert_called_once()


if __name__ == '__main__':
    unittest.main()
