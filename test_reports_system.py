#!/usr/bin/env python
"""
Test the reports and analytics system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.db import models
from django.utils import timezone
from course_matcher.models import (
    CounselorStudentAssignment, CounselorFeedback, StudentProfile
)
from course_matcher.views import get_profile_completion_status

def test_reports_page():
    """Test the reports and analytics page"""
    print("=== Testing Reports Page ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test the reports page
    url = reverse('counselor_reports')
    response = client.get(url)
    
    if response.status_code == 200:
        print("✓ Reports page loads successfully")
        
        # Check for key sections
        content_checks = [
            (b'Reports & Analytics', 'Page title'),
            (b'Total Students', 'Overview statistics'),
            (b'Profile Completion Distribution', 'Completion chart'),
            (b'GPA Distribution', 'GPA chart'),
            (b'Major Distribution', 'Major chart'),
            (b'Feedback & Communication Analytics', 'Feedback analytics'),
            (b'Export Options', 'Export section'),
        ]
        
        for content, description in content_checks:
            if content in response.content:
                print(f"✓ {description} found on page")
            else:
                print(f"❌ {description} not found on page")
                
    else:
        print(f"❌ Reports page failed: {response.status_code}")
        return False
    
    return True

def test_analytics_calculations():
    """Test the analytics calculations"""
    print("\n=== Testing Analytics Calculations ===")
    
    # Get counselor's assigned students
    counselor_user = User.objects.get(username='counselor')
    counselor = counselor_user.guidancecounselorprofile
    
    assigned_students = CounselorStudentAssignment.objects.filter(
        counselor=counselor,
        is_active=True
    ).select_related('student__user', 'student__major')
    
    total_students = assigned_students.count()
    print(f"✓ Total assigned students: {total_students}")
    
    # Calculate profile completion stats
    completion_stats = {
        'complete': 0,
        'partial': 0,
        'minimal': 0
    }
    
    gpa_stats = {
        'excellent': 0,  # 3.5+
        'good': 0,       # 3.0-3.49
        'average': 0,    # 2.5-2.99
        'below_average': 0  # <2.5
    }
    
    major_distribution = {}
    
    for assignment in assigned_students:
        student = assignment.student
        progress = get_profile_completion_status(student)
        completed_count = sum(progress.values())
        
        # Profile completion
        if completed_count == 6:
            completion_stats['complete'] += 1
        elif completed_count >= 3:
            completion_stats['partial'] += 1
        else:
            completion_stats['minimal'] += 1
        
        # GPA distribution
        if student.gpa:
            gpa = float(student.gpa)
            if gpa >= 3.5:
                gpa_stats['excellent'] += 1
            elif gpa >= 3.0:
                gpa_stats['good'] += 1
            elif gpa >= 2.5:
                gpa_stats['average'] += 1
            else:
                gpa_stats['below_average'] += 1
        
        # Major distribution
        major_name = student.major.name if student.major else 'Undeclared'
        major_distribution[major_name] = major_distribution.get(major_name, 0) + 1
    
    print(f"✓ Profile completion stats: {completion_stats}")
    print(f"✓ GPA distribution: {gpa_stats}")
    print(f"✓ Major distribution: {major_distribution}")
    
    # Feedback analytics
    feedback_stats = CounselorFeedback.objects.filter(counselor=counselor).aggregate(
        total_feedback=models.Count('id'),
        pending_follow_ups=models.Count('id', filter=models.Q(follow_up_required=True, follow_up_date__gte=timezone.now())),
        overdue_follow_ups=models.Count('id', filter=models.Q(follow_up_required=True, follow_up_date__lt=timezone.now()))
    )
    
    print(f"✓ Feedback stats: {feedback_stats}")
    
    return True

def test_export_functionality():
    """Test export functionality"""
    print("\n=== Testing Export Functionality ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    url = reverse('counselor_reports')
    response = client.get(url)
    
    if response.status_code == 200:
        # Check for export buttons
        export_checks = [
            (b'Student Progress', 'Student progress export'),
            (b'Feedback Report', 'Feedback report export'),
            (b'GPA Analysis', 'GPA analysis export'),
            (b'Intervention Report', 'Intervention report export'),
            (b'Export All Reports', 'Export all button'),
        ]
        
        for content, description in export_checks:
            if content in response.content:
                print(f"✓ {description} button found")
            else:
                print(f"❌ {description} button not found")
        
        # Check for JavaScript export functions
        js_functions = [
            b'exportStudentProgress',
            b'exportFeedbackReport',
            b'exportGPAAnalysis',
            b'exportInterventionReport',
            b'exportAllReports',
        ]
        
        for function in js_functions:
            if function in response.content:
                print(f"✓ {function.decode()} function found")
            else:
                print(f"❌ {function.decode()} function not found")
    
    return True

def test_chart_rendering():
    """Test chart rendering and data visualization"""
    print("\n=== Testing Chart Rendering ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    url = reverse('counselor_reports')
    response = client.get(url)
    
    if response.status_code == 200:
        # Check for chart elements
        chart_checks = [
            (b'Profile Completion Distribution', 'Profile completion chart'),
            (b'GPA Distribution', 'GPA distribution chart'),
            (b'Major Distribution', 'Major distribution chart'),
            (b'widthratio', 'Progress bar calculations'),
        ]
        
        for content, description in chart_checks:
            if content in response.content:
                print(f"✓ {description} found")
            else:
                print(f"❌ {description} not found")
        
        # Check for proper percentage calculations
        if b'%' in response.content:
            print("✓ Percentage calculations found in charts")
        else:
            print("❌ Percentage calculations not found")
    
    return True

def test_responsive_design():
    """Test responsive design elements"""
    print("\n=== Testing Responsive Design ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    url = reverse('counselor_reports')
    response = client.get(url)
    
    if response.status_code == 200:
        # Check for responsive grid classes
        responsive_checks = [
            (b'grid-cols-1', 'Single column grid'),
            (b'md:grid-cols-', 'Medium screen grid'),
            (b'lg:grid-cols-', 'Large screen grid'),
            (b'backdrop-blur-sm', 'Glassmorphism effect'),
            (b'rounded-xl', 'Rounded corners'),
            (b'shadow-lg', 'Shadow effects'),
        ]
        
        for content, description in responsive_checks:
            if content in response.content:
                print(f"✓ {description} found")
            else:
                print(f"❌ {description} not found")
    
    return True

def main():
    """Run all reports system tests"""
    print("CourseRec Reports System Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_reports_page()
        success &= test_analytics_calculations()
        success &= test_export_functionality()
        success &= test_chart_rendering()
        success &= test_responsive_design()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All reports system tests passed!")
            print("The reports and analytics system is working correctly.")
        else:
            print("❌ Some reports system tests failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
