# Generated by Django 5.2.3 on 2025-07-07 14:50

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('course_matcher', '0002_admissiontest_admissiontestattempt_studentsurvey_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='admissiontest',
            name='difficulty_level',
            field=models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], default='medium', max_length=20),
        ),
        migrations.AlterField(
            model_name='admissiontest',
            name='subject_area',
            field=models.CharField(choices=[('math', 'Mathematics'), ('science', 'Science'), ('english', 'English'), ('history', 'History'), ('general_knowledge', 'General Knowledge')], max_length=30),
        ),
        migrations.CreateModel(
            name='GuidanceCounselorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True)),
                ('specialization', models.CharField(choices=[('academic', 'Academic Counseling'), ('career', 'Career Guidance'), ('personal', 'Personal Development'), ('general', 'General Counseling')], default='general', max_length=20)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('office_location', models.CharField(blank=True, max_length=100)),
                ('office_hours', models.TextField(blank=True, help_text='Office hours schedule')),
                ('bio', models.TextField(blank=True, help_text='Professional biography')),
                ('years_experience', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('departments', models.ManyToManyField(blank=True, help_text='Departments this counselor is responsible for', to='course_matcher.department')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user__last_name', 'user__first_name'],
            },
        ),
        migrations.CreateModel(
            name='CounselorFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(choices=[('general', 'General Feedback'), ('academic', 'Academic Guidance'), ('career', 'Career Advice'), ('course_recommendation', 'Course Recommendation'), ('intervention', 'Academic Intervention')], default='general', max_length=30)),
                ('priority', models.CharField(choices=[('low', 'Low Priority'), ('medium', 'Medium Priority'), ('high', 'High Priority'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('recommendations', models.JSONField(default=list, help_text='List of specific recommendations')),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
                ('counselor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.guidancecounselorprofile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RecommendationApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('modified', 'Approved with Modifications'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('counselor_notes', models.TextField(blank=True, help_text="Counselor's notes on the recommendation")),
                ('modified_reasoning', models.TextField(blank=True, help_text='Modified reasoning if approved with changes')),
                ('reviewed_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('counselor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.guidancecounselorprofile')),
                ('recommendation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.recommendation')),
            ],
            options={
                'ordering': ['-reviewed_at'],
            },
        ),
        migrations.CreateModel(
            name='CounselorStudentAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_date', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, help_text='Assignment notes or special considerations')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
                ('counselor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.guidancecounselorprofile')),
            ],
            options={
                'ordering': ['-assigned_date'],
                'unique_together': {('counselor', 'student')},
            },
        ),
    ]
