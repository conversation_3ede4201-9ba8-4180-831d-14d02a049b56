#!/usr/bin/env python
"""
Test database integration and CRUD operations
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client, TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from course_matcher.models import (
    GuidanceCounselorProfile, CounselorStudentAssignment, 
    CounselorFeedback, Recommendation, RecommendationApproval,
    StudentProfile, Course
)

def test_counselor_model_operations():
    """Test CRUD operations on counselor models"""
    print("=== Testing Counselor Model Operations ===")
    
    # Test counselor profile access
    counselor_user = User.objects.get(username='counselor')
    counselor = counselor_user.guidancecounselorprofile
    
    print(f"✓ Counselor profile access: {counselor.employee_id}")
    print(f"  - Specialization: {counselor.get_specialization_display()}")
    print(f"  - Years experience: {counselor.years_experience}")
    print(f"  - Is active: {counselor.is_active}")
    
    # Test student assignments
    assignments = CounselorStudentAssignment.objects.filter(
        counselor=counselor,
        is_active=True
    )
    print(f"✓ Student assignments: {assignments.count()}")
    
    for assignment in assignments[:3]:
        print(f"  - {assignment.student.user.get_full_name()} (assigned: {assignment.assigned_date})")
    
    return True

def test_feedback_crud_operations():
    """Test feedback CRUD operations"""
    print("\n=== Testing Feedback CRUD Operations ===")
    
    counselor = User.objects.get(username='counselor').guidancecounselorprofile
    assignment = CounselorStudentAssignment.objects.filter(
        counselor=counselor,
        is_active=True
    ).first()
    
    if not assignment:
        print("❌ No student assignments found")
        return False
    
    student = assignment.student
    
    # Test CREATE
    feedback_data = {
        'student': student,
        'counselor': counselor,
        'feedback_type': 'academic',
        'priority': 'medium',
        'title': 'Test Database Integration Feedback',
        'content': 'This is a test feedback to verify database integration.',
        'recommendations': ['Study more effectively', 'Attend office hours', 'Join study groups'],
        'follow_up_required': True,
        'follow_up_date': timezone.now() + timedelta(days=7)
    }
    
    feedback = CounselorFeedback.objects.create(**feedback_data)
    print(f"✓ CREATE: Feedback created with ID {feedback.id}")
    
    # Test READ
    retrieved_feedback = CounselorFeedback.objects.get(id=feedback.id)
    print(f"✓ READ: Retrieved feedback '{retrieved_feedback.title}'")
    print(f"  - Type: {retrieved_feedback.get_feedback_type_display()}")
    print(f"  - Priority: {retrieved_feedback.get_priority_display()}")
    print(f"  - Recommendations count: {len(retrieved_feedback.recommendations)}")
    
    # Test UPDATE
    retrieved_feedback.priority = 'high'
    retrieved_feedback.content += ' [UPDATED]'
    retrieved_feedback.save()
    
    updated_feedback = CounselorFeedback.objects.get(id=feedback.id)
    print(f"✓ UPDATE: Priority changed to {updated_feedback.get_priority_display()}")
    
    # Test relationships
    student_feedback = CounselorFeedback.objects.filter(student=student)
    counselor_feedback = CounselorFeedback.objects.filter(counselor=counselor)
    print(f"✓ RELATIONSHIPS: Student has {student_feedback.count()} feedback records")
    print(f"✓ RELATIONSHIPS: Counselor has {counselor_feedback.count()} feedback records")
    
    # Test DELETE
    feedback_id = feedback.id
    feedback.delete()
    
    try:
        CounselorFeedback.objects.get(id=feedback_id)
        print("❌ DELETE: Feedback still exists after deletion")
        return False
    except CounselorFeedback.DoesNotExist:
        print("✓ DELETE: Feedback successfully deleted")
    
    return True

def test_recommendation_approval_operations():
    """Test recommendation approval CRUD operations"""
    print("\n=== Testing Recommendation Approval Operations ===")
    
    counselor = User.objects.get(username='counselor').guidancecounselorprofile
    
    # Get a recommendation for testing
    recommendation = Recommendation.objects.filter(
        student__in=CounselorStudentAssignment.objects.filter(
            counselor=counselor,
            is_active=True
        ).values_list('student', flat=True)
    ).first()
    
    if not recommendation:
        print("❌ No recommendations found for testing")
        return False
    
    # Test CREATE approval
    approval_data = {
        'recommendation': recommendation,
        'counselor': counselor,
        'status': 'approved',
        'counselor_notes': 'Test approval for database integration testing.',
        'reviewed_date': timezone.now()
    }
    
    approval = RecommendationApproval.objects.create(**approval_data)
    print(f"✓ CREATE: Approval created with ID {approval.id}")
    
    # Test READ
    retrieved_approval = RecommendationApproval.objects.get(id=approval.id)
    print(f"✓ READ: Retrieved approval for {retrieved_approval.recommendation.course.code}")
    print(f"  - Status: {retrieved_approval.get_status_display()}")
    print(f"  - Reviewed date: {retrieved_approval.reviewed_date}")
    
    # Test UPDATE
    retrieved_approval.status = 'modified'
    retrieved_approval.modified_reasoning = 'Modified for testing purposes'
    retrieved_approval.save()
    
    updated_approval = RecommendationApproval.objects.get(id=approval.id)
    print(f"✓ UPDATE: Status changed to {updated_approval.get_status_display()}")
    
    # Test relationships
    rec_approvals = RecommendationApproval.objects.filter(recommendation=recommendation)
    counselor_approvals = RecommendationApproval.objects.filter(counselor=counselor)
    print(f"✓ RELATIONSHIPS: Recommendation has {rec_approvals.count()} approval records")
    print(f"✓ RELATIONSHIPS: Counselor has {counselor_approvals.count()} approval records")
    
    # Clean up
    approval.delete()
    print("✓ DELETE: Test approval cleaned up")
    
    return True

def test_data_persistence():
    """Test data persistence across requests"""
    print("\n=== Testing Data Persistence ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Get a student for testing
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if not assignment:
        print("❌ No student assignments found")
        return False
    
    student = assignment.student
    
    # Create feedback via web interface
    url = reverse('counselor_feedback_create', args=[student.id])
    feedback_data = {
        'feedback_type': 'career',
        'priority': 'high',
        'title': 'Data Persistence Test Feedback',
        'content': 'Testing data persistence across web requests.',
        'recommendations_text': 'Test recommendation 1\nTest recommendation 2',
        'follow_up_required': True,
        'follow_up_date': (timezone.now() + timedelta(days=5)).strftime('%Y-%m-%dT%H:%M'),
    }
    
    response = client.post(url, feedback_data)
    
    if response.status_code == 302:  # Successful redirect
        print("✓ Feedback submitted via web interface")
        
        # Verify data was saved
        feedback = CounselorFeedback.objects.filter(
            student=student,
            title='Data Persistence Test Feedback'
        ).first()
        
        if feedback:
            print("✓ Data persisted to database")
            print(f"  - Title: {feedback.title}")
            print(f"  - Type: {feedback.get_feedback_type_display()}")
            print(f"  - Recommendations: {len(feedback.recommendations)}")
            
            # Clean up
            feedback.delete()
            print("✓ Test data cleaned up")
        else:
            print("❌ Data not persisted to database")
            return False
    else:
        print(f"❌ Feedback submission failed: {response.status_code}")
        return False
    
    return True

def test_foreign_key_relationships():
    """Test foreign key relationships and constraints"""
    print("\n=== Testing Foreign Key Relationships ===")
    
    # Test counselor-student assignment relationship
    counselor = User.objects.get(username='counselor').guidancecounselorprofile
    assignments = counselor.counselorstudentassignment_set.filter(is_active=True)
    print(f"✓ Counselor has {assignments.count()} active student assignments")
    
    # Test student-feedback relationship
    if assignments.exists():
        student = assignments.first().student
        feedback_count = student.counselorfeedback_set.count()
        print(f"✓ Student {student.user.get_full_name()} has {feedback_count} feedback records")
        
        # Test counselor-feedback relationship
        counselor_feedback_count = counselor.counselorfeedback_set.count()
        print(f"✓ Counselor has {counselor_feedback_count} feedback records")
    
    # Test recommendation-approval relationship
    recommendations = Recommendation.objects.filter(
        student__in=assignments.values_list('student', flat=True)
    )
    
    if recommendations.exists():
        rec = recommendations.first()
        approval_count = rec.recommendationapproval_set.count()
        print(f"✓ Recommendation {rec.course.code} has {approval_count} approval records")
    
    return True

def main():
    """Run all database integration tests"""
    print("CourseRec Database Integration Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_counselor_model_operations()
        success &= test_feedback_crud_operations()
        success &= test_recommendation_approval_operations()
        success &= test_data_persistence()
        success &= test_foreign_key_relationships()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All database integration tests passed!")
            print("Database operations are working correctly.")
        else:
            print("❌ Some database integration tests failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
