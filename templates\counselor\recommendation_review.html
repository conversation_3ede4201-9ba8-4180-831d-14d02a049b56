{% extends 'base.html' %}

{% block title %}Review Recommendation - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Review Course Recommendation</h1>
            <p class="text-gray-600">{{ recommendation.course.code }} for {{ student.user.get_full_name }}</p>
        </div>
        <div class="flex items-center space-x-4">
            <a href="{% url 'counselor_recommendations' %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Recommendations
            </a>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Student & Course Info -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Student Profile -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Student Profile</h2>
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                    {{ student.user.first_name|slice:":1" }}{{ student.user.last_name|slice:":1" }}
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900">{{ student.user.get_full_name }}</h3>
                    <p class="text-gray-600">{{ student.major.name|default:"No major selected" }}</p>
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="text-sm text-gray-500">{{ student.get_year_display }}</span>
                        <span class="text-sm text-gray-500">GPA: {{ student.gpa|default:"N/A" }}</span>
                        <span class="text-sm text-gray-500">{{ student.total_credits }} credits</span>
                    </div>
                </div>
            </div>
            
            <!-- Profile Completion -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-800 mb-2">Profile Completion</h4>
                <div class="grid grid-cols-3 gap-2">
                    {% for component, completed in progress.items %}
                        <div class="flex items-center justify-center p-2 rounded {% if completed %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            <span class="text-xs font-medium">
                                {% if component == 'academic_records' %}📚 Records
                                {% elif component == 'interests' %}🎯 Interests
                                {% elif component == 'career_goals' %}💼 Goals
                                {% elif component == 'major' %}🎓 Major
                                {% elif component == 'admission_test' %}📝 Test
                                {% elif component == 'survey' %}📋 Survey
                                {% endif %}
                            </span>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Course Recommendation Details -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Course Recommendation</h2>
            
            <!-- Course Header -->
            <div class="bg-blue-50 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-2xl font-bold text-blue-900">{{ recommendation.course.code }}</h3>
                        <h4 class="text-xl font-semibold text-blue-800">{{ recommendation.course.name }}</h4>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-blue-900">{{ recommendation.confidence_score|floatformat:2 }}</div>
                        <div class="text-sm text-blue-700">Confidence Score</div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-blue-800">Department:</span>
                        <span class="text-blue-700">{{ recommendation.course.department.name }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-blue-800">Credits:</span>
                        <span class="text-blue-700">{{ recommendation.course.credits }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-blue-800">Difficulty:</span>
                        <span class="text-blue-700">{{ recommendation.course.get_difficulty_display }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-blue-800">Type:</span>
                        <span class="text-blue-700">{{ recommendation.get_recommendation_type_display }}</span>
                    </div>
                </div>
            </div>

            <!-- Course Description -->
            <div class="mb-6">
                <h4 class="font-semibold text-gray-800 mb-2">Course Description</h4>
                <p class="text-gray-700">{{ recommendation.course.description }}</p>
            </div>

            <!-- ML Reasoning -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 class="font-semibold text-gray-800 mb-2">ML Algorithm Reasoning</h4>
                <p class="text-gray-700">{{ recommendation.reasoning }}</p>
            </div>

            <!-- Prerequisites -->
            {% if recommendation.course.prerequisites.all %}
            <div class="mb-6">
                <h4 class="font-semibold text-gray-800 mb-2">Prerequisites</h4>
                <div class="flex flex-wrap gap-2">
                    {% for prereq in recommendation.course.prerequisites.all %}
                        {% with student_has_prereq=academic_records|length %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                {{ prereq.code }}
                            </span>
                        {% endwith %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Review Form -->
    <div class="space-y-6">
        <!-- Current Status -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Current Status</h3>
            <div class="text-center">
                <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium
                    {% if approval.status == 'pending' %}bg-orange-100 text-orange-800
                    {% elif approval.status == 'approved' %}bg-green-100 text-green-800
                    {% elif approval.status == 'modified' %}bg-blue-100 text-blue-800
                    {% elif approval.status == 'rejected' %}bg-red-100 text-red-800
                    {% endif %}">
                    {{ approval.get_status_display }}
                </span>
                {% if approval.reviewed_at %}
                    <p class="text-sm text-gray-500 mt-2">Last reviewed {{ approval.reviewed_at|timesince }} ago</p>
                {% endif %}
            </div>
        </div>

        <!-- Review Form -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Review Decision</h3>
            
            <form method="post" class="space-y-4">
                {% csrf_token %}
                
                <!-- Status Selection -->
                <div>
                    <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Decision
                    </label>
                    {{ form.status }}
                    {% if form.status.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.status.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Counselor Notes -->
                <div>
                    <label for="{{ form.counselor_notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Your Notes <span class="text-red-500">*</span>
                    </label>
                    {{ form.counselor_notes }}
                    {% if form.counselor_notes.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.counselor_notes.errors.0 }}</div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">Explain your decision and provide guidance for the student.</p>
                </div>

                <!-- Modified Reasoning -->
                <div id="modified-reasoning-section" style="display: none;">
                    <label for="{{ form.modified_reasoning.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Modified Reasoning
                    </label>
                    {{ form.modified_reasoning }}
                    {% if form.modified_reasoning.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.modified_reasoning.errors.0 }}</div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">Provide updated reasoning that will be shown to the student.</p>
                </div>

                <!-- Submit Buttons -->
                <div class="flex space-x-3 pt-4">
                    <button type="submit" 
                            class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                        Save Review
                    </button>
                    <a href="{% url 'counselor_recommendations' %}" 
                       class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                        Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="{% url 'counselor_student_detail' student.id %}" 
                   class="flex items-center p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors group">
                    <svg class="w-5 h-5 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H2z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">View Full Student Profile</span>
                </a>
                
                <a href="{% url 'counselor_feedback_create' student.id %}" 
                   class="flex items-center p-3 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors group">
                    <svg class="w-5 h-5 text-purple-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">Send Additional Feedback</span>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide modified reasoning field based on status selection
document.getElementById('{{ form.status.id_for_label }}').addEventListener('change', function() {
    const modifiedSection = document.getElementById('modified-reasoning-section');
    if (this.value === 'modified') {
        modifiedSection.style.display = 'block';
    } else {
        modifiedSection.style.display = 'none';
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    const statusField = document.getElementById('{{ form.status.id_for_label }}');
    const modifiedSection = document.getElementById('modified-reasoning-section');
    if (statusField.value === 'modified') {
        modifiedSection.style.display = 'block';
    }
});
</script>
{% endblock %}
