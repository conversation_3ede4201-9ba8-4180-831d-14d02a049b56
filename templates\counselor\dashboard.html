{% extends 'base.html' %}

{% block title %}Guidance Counselor Dashboard - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Quick Stats Cards with Glassmorphism -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Assigned Students -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Assigned Students</h3>
                <p class="text-3xl font-bold text-primary-600">{{ total_assigned_students|default:0 }}</p>
                <p class="text-xs text-gray-500 mt-1">Under your guidance</p>
            </div>
            <div class="p-3 bg-primary-100 rounded-full">
                <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H2z"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Profile Completion Rate -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Profile Completion</h3>
                <p class="text-3xl font-bold text-green-600">{{ profile_completion_rate|floatformat:0 }}%</p>
                <p class="text-xs text-gray-500 mt-1">{{ students_with_complete_profiles }} of {{ total_assigned_students }} complete</p>
            </div>
            <div class="p-3 bg-green-100 rounded-full">
                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Recommendations -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Pending Reviews</h3>
                <p class="text-3xl font-bold text-orange-600">{{ pending_recommendations|default:0 }}</p>
                <p class="text-xs text-gray-500 mt-1">Recommendations to review</p>
            </div>
            <div class="p-3 bg-orange-100 rounded-full">
                <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Recent Feedback -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">This Week</h3>
                <p class="text-3xl font-bold text-blue-600">{{ recent_feedback_count|default:0 }}</p>
                <p class="text-xs text-gray-500 mt-1">Feedback provided</p>
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Students Needing Attention -->
    <div class="lg:col-span-2">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800">Students Needing Attention</h2>
                <a href="{% url 'counselor_students' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</a>
            </div>
            
            {% if students_needing_attention %}
                <div class="space-y-4">
                    {% for item in students_needing_attention %}
                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-100">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                                {{ item.student.user.first_name|slice:":1" }}{{ item.student.user.last_name|slice:":1" }}
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">{{ item.student.user.get_full_name }}</h3>
                                <p class="text-sm text-gray-600">{{ item.student.major.name|default:"No major selected" }}</p>
                                <div class="flex items-center mt-1">
                                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-red-500 h-2 rounded-full" style="width: {{ item.completed_count|mul:16.67 }}%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500">{{ item.completed_count }}/6 complete</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <a href="{% url 'counselor_student_detail' item.student.id %}" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                                Review
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">All students on track!</h3>
                    <p class="mt-1 text-sm text-gray-500">No students currently need immediate attention.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions & Recent Activity -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="{% url 'counselor_students' %}" class="flex items-center p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors group">
                    <svg class="w-5 h-5 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H2z"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Manage Students</p>
                        <p class="text-xs text-gray-500">View and track student progress</p>
                    </div>
                </a>
                
                <a href="{% url 'counselor_recommendations' %}" class="flex items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors group">
                    <svg class="w-5 h-5 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Review Recommendations</p>
                        <p class="text-xs text-gray-500">Approve course suggestions</p>
                    </div>
                </a>
                
                <a href="{% url 'counselor_feedback' %}" class="flex items-center p-3 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors group">
                    <svg class="w-5 h-5 text-purple-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Provide Feedback</p>
                        <p class="text-xs text-gray-500">Send guidance to students</p>
                    </div>
                </a>
                
                <a href="{% url 'counselor_reports' %}" class="flex items-center p-3 rounded-lg bg-orange-50 hover:bg-orange-100 transition-colors group">
                    <svg class="w-5 h-5 text-orange-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Generate Reports</p>
                        <p class="text-xs text-gray-500">View analytics and trends</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
            {% if recent_feedback %}
                <div class="space-y-3">
                    {% for feedback in recent_feedback %}
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            {{ feedback.student.user.first_name|slice:":1" }}
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ feedback.title }}</p>
                            <p class="text-xs text-gray-500">{{ feedback.student.user.get_full_name }}</p>
                            <p class="text-xs text-gray-400">{{ feedback.created_at|timesince }} ago</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-sm text-gray-500">No recent activity</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
