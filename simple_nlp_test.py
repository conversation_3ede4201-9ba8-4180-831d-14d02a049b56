#!/usr/bin/env python
"""
Simple test to verify NLP enhancement is working with existing data
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from course_matcher.models import StudentProfile, Course
from course_matcher.recommendation_service import RecommendationEngine


def test_basic_functionality():
    """Test basic functionality with existing data"""
    print("🔍 Testing NLP Enhancement with Existing Data")
    print("=" * 50)
    
    # Check existing data
    courses = Course.objects.all()
    students = StudentProfile.objects.all()
    
    print(f"📊 Database Status:")
    print(f"   Courses: {courses.count()}")
    print(f"   Students: {students.count()}")
    
    if courses.count() == 0:
        print("❌ No courses found in database")
        return False
    
    if students.count() == 0:
        print("❌ No students found in database")
        return False
    
    # Test recommendation engine initialization
    print(f"\n🤖 Testing Enhanced Recommendation Engine:")
    try:
        engine = RecommendationEngine()
        print("   ✓ Engine initialized successfully")
        
        # Check if NLP service is available
        if engine.nlp_service:
            print("   ✓ NLP service is available")
        else:
            print("   ⚠️  NLP service not available (fallback mode)")
        
        # Test with first student
        student = students.first()
        print(f"\n👤 Testing with student: {student.user.get_full_name()}")
        print(f"   Student ID: {student.student_id}")
        print(f"   Year: {student.year}")
        print(f"   Major: {student.major}")
        
        # Generate recommendations
        print(f"\n🎯 Generating recommendations...")
        recommendations = engine.get_recommendations(student, limit=3)
        
        print(f"   Generated {len(recommendations)} recommendations")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"\n   {i}. {rec.course.code} - {rec.course.name}")
            print(f"      Confidence: {rec.confidence_score:.2f}")
            print(f"      Type: {rec.recommendation_type}")
            print(f"      Reasoning: {rec.reasoning[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_nlp_service_directly():
    """Test NLP service directly if available"""
    print(f"\n🧠 Testing NLP Service Directly:")
    
    try:
        from course_matcher.nlp_service import GeminiNLPService
        
        # Check if API key is available
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("   ⚠️  GEMINI_API_KEY not found in environment")
            print("   ℹ️  NLP features will use fallback mode")
            return False
        
        print("   ✓ API key found")
        
        # Try to initialize service
        nlp_service = GeminiNLPService()
        print("   ✓ NLP service initialized")
        
        # Test basic functionality (this will use actual API if key is valid)
        print("   🔍 Testing semantic similarity...")
        similarity = nlp_service.analyze_semantic_similarity(
            "computer science programming",
            "software development coding",
            "academic course matching"
        )
        
        print(f"   ✓ Similarity score: {similarity.score:.2f}")
        print(f"   ✓ Explanation: {similarity.explanation[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ NLP service error: {e}")
        print("   ℹ️  This is expected if API key is invalid or quota exceeded")
        return False


def show_enhancement_features():
    """Show what enhancements have been added"""
    print(f"\n✨ NLP Enhancement Features Added:")
    print("=" * 50)
    
    features = [
        "🤖 Semantic Similarity Analysis",
        "📝 Natural Language Explanations", 
        "🧠 Content Understanding",
        "⚡ Intelligent Caching",
        "🛡️  Error Handling & Fallbacks",
        "📊 Enhanced Scoring Algorithm",
        "🎨 Improved Frontend UI",
        "🔧 Comprehensive Testing"
    ]
    
    for feature in features:
        print(f"   ✓ {feature}")
    
    print(f"\n📁 Files Modified/Added:")
    files = [
        "course_matcher/nlp_service.py (NEW)",
        "course_matcher/recommendation_service.py (ENHANCED)",
        "templates/student/recommendations.html (ENHANCED)",
        "CourseRec/settings.py (UPDATED)",
        "course_matcher/test_nlp_service.py (NEW)",
        "NLP_ENHANCEMENT_DOCUMENTATION.md (NEW)"
    ]
    
    for file in files:
        print(f"   📄 {file}")


def main():
    """Main test function"""
    print("🚀 CourseRec NLP Enhancement Verification")
    print("=" * 60)
    
    # Test basic functionality
    basic_success = test_basic_functionality()
    
    # Test NLP service if available
    nlp_success = test_nlp_service_directly()
    
    # Show enhancement features
    show_enhancement_features()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    
    if basic_success:
        print("   ✅ Enhanced recommendation engine working")
    else:
        print("   ❌ Enhanced recommendation engine failed")
    
    if nlp_success:
        print("   ✅ NLP service fully functional")
    else:
        print("   ⚠️  NLP service in fallback mode")
    
    print(f"\n💡 Next Steps:")
    if not nlp_success:
        print("   1. Ensure GEMINI_API_KEY is set in .env file")
        print("   2. Verify API key has available quota")
        print("   3. Check internet connectivity")
    else:
        print("   1. ✅ System is fully operational!")
        print("   2. 🎉 NLP enhancement is working perfectly!")
    
    print(f"\n📖 For detailed documentation, see:")
    print("   📄 NLP_ENHANCEMENT_DOCUMENTATION.md")


if __name__ == "__main__":
    main()
