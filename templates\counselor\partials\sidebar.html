    <div class="h-full flex flex-col">
        <!-- Mobile close button -->
        <div class="sidebar-close-btn lg:hidden">
            <button @click="sidebarOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <!-- Header/App Title -->
        <div class="px-6 border-b border-gray-200 flex items-center" style="height:72px;min-height:72px;">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div>
                    <h2 class="text-lg font-bold text-gray-900">Counselor Portal</h2>
                    <p class="text-xs text-gray-500">Student Guidance</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2">
            <a href="{% url 'counselor_dashboard' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if request.resolver_match.url_name == 'counselor_dashboard' %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if request.resolver_match.url_name == 'counselor_dashboard' %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                </svg>
                <span>Dashboard</span>
            </a>
            
            <a href="{% url 'counselor_students' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if request.resolver_match.url_name == 'counselor_students' %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if request.resolver_match.url_name == 'counselor_students' %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H2z"/>
                </svg>
                <span>My Students</span>
            </a>
            
            <a href="{% url 'counselor_student_progress' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if 'progress' in request.resolver_match.url_name %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if 'progress' in request.resolver_match.url_name %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span>Progress Tracking</span>
            </a>
            
            <a href="{% url 'counselor_recommendations' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if 'recommendation' in request.resolver_match.url_name %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if 'recommendation' in request.resolver_match.url_name %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
                </svg>
                <span>Course Recommendations</span>
            </a>
            
            <a href="{% url 'counselor_feedback' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if 'feedback' in request.resolver_match.url_name %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if 'feedback' in request.resolver_match.url_name %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                <span>Student Feedback</span>
            </a>
            
            <a href="{% url 'counselor_interventions' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if 'intervention' in request.resolver_match.url_name %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if 'intervention' in request.resolver_match.url_name %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <span>Interventions</span>
            </a>
            
            <a href="{% url 'counselor_reports' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 {% if 'report' in request.resolver_match.url_name %}bg-purple-50 text-purple-700{% endif %}">
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-purple-600 {% if 'report' in request.resolver_match.url_name %}text-purple-600{% endif %}"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                </svg>
                <span>Reports & Analytics</span>
            </a>
        </nav>

        <!-- Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-user">
                <div class="sidebar-avatar">
                    {{ user.get_full_name|default:user.username|slice:":1"|upper }}
                </div>
                <div class="sidebar-user-info">
                    <p>{{ user.get_full_name|default:user.username }}</p>
                    <span>Guidance Counselor</span>
                </div>
                <form method="post" action="{% url 'logout' %}" class="ml-auto">
                    {% csrf_token %}
                    <button type="submit" class="bg-transparent border-0 p-0 text-gray-400 hover:text-red-500 transition-colors duration-200" title="Logout">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>

