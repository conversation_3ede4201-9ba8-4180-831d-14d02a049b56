Stack trace:
Frame         Function      Args
0007FFFF7CF0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF6BF0) msys-2.0.dll+0x1FEBA
0007FFFF7CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7FC8) msys-2.0.dll+0x67F9
0007FFFF7CF0  000210046832 (000210285FF9, 0007FFFF7BA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7CF0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF7CF0  0002100690B4 (0007FFFF7D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF7FD0  00021006A49D (0007FFFF7D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB566A0000 ntdll.dll
7FFB54D60000 KERNEL32.DLL
7FFB537F0000 KERNELBASE.dll
7FFB562A0000 USER32.dll
000210040000 msys-2.0.dll
7FFB54290000 win32u.dll
7FFB55830000 GDI32.dll
7FFB53BE0000 gdi32full.dll
7FFB53D20000 msvcp_win.dll
7FFB53F50000 ucrtbase.dll
7FFB544D0000 advapi32.dll
7FFB54AD0000 msvcrt.dll
7FFB54EE0000 sechost.dll
7FFB55090000 RPCRT4.dll
7FFB52DA0000 CRYPTBASE.DLL
7FFB541F0000 bcryptPrimitives.dll
7FFB545B0000 IMM32.DLL
7FFB396C0000 windhawk.dll
7FFB4CBE0000 WINHTTP.dll
