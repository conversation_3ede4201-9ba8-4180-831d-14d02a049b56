#!/usr/bin/env python
"""
Test recommendation access for counselors
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.contrib.auth.models import User
from course_matcher.models import (
    GuidanceCounselorProfile, CounselorStudentAssignment, 
    Recommendation, RecommendationApproval
)

def test_recommendation_access():
    """Test if counselor can access recommendations"""
    print("=== Testing Recommendation Access ===")
    
    # Get counselor
    counselor_user = User.objects.get(username='counselor')
    counselor = counselor_user.guidancecounselorprofile
    
    # Get assigned students
    assigned_students = CounselorStudentAssignment.objects.filter(
        counselor=counselor,
        is_active=True
    ).values_list('student', flat=True)
    
    print(f"✓ Assigned students: {list(assigned_students)}")
    
    # Get recommendations for assigned students
    recommendations = Recommendation.objects.filter(
        student__in=assigned_students
    )
    
    print(f"✓ Recommendations for assigned students: {recommendations.count()}")
    
    if recommendations.count() > 0:
        # Test first recommendation
        rec = recommendations.first()
        print(f"✓ Testing recommendation: {rec.id} - {rec.course.code} for {rec.student.user.get_full_name()}")
        
        # Check if counselor has access to this student
        assignment = CounselorStudentAssignment.objects.filter(
            counselor=counselor,
            student=rec.student,
            is_active=True
        ).first()
        
        print(f"✓ Counselor has access to student: {assignment is not None}")
        
        # Check if approval record exists
        approval = RecommendationApproval.objects.filter(
            recommendation=rec,
            counselor=counselor
        ).first()
        
        print(f"✓ Approval record exists: {approval is not None}")
        
        return rec.id
    else:
        print("❌ No recommendations found for assigned students")
        
        # Let's create some recommendations for testing
        print("Creating test recommendations...")
        
        if assigned_students:
            from course_matcher.models import Course
            student_id = assigned_students[0]
            student = StudentProfile.objects.get(id=student_id)
            
            # Get some courses to recommend
            courses = Course.objects.all()[:3]
            
            for course in courses:
                rec, created = Recommendation.objects.get_or_create(
                    student=student,
                    course=course,
                    defaults={
                        'confidence_score': 0.85,
                        'reasoning': f'Test recommendation for {course.code} based on student profile analysis.',
                        'recommendation_type': 'core'
                    }
                )
                if created:
                    print(f"  Created recommendation: {rec.course.code} for {rec.student.user.get_full_name()}")
            
            return Recommendation.objects.filter(student=student).first().id
        
        return None

def main():
    """Run recommendation access test"""
    print("CourseRec Recommendation Access Test")
    print("=" * 50)
    
    try:
        rec_id = test_recommendation_access()
        
        if rec_id:
            print(f"\n✅ Test recommendation ID: {rec_id}")
            print("You can now test the recommendation review view with this ID.")
        else:
            print("\n❌ No recommendations available for testing")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
