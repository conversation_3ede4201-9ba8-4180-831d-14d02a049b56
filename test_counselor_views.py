#!/usr/bin/env python
"""
Test script for counselor views and templates
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse

def test_counselor_views():
    """Test all counselor views"""
    print("=== Testing Counselor Views ===")
    
    client = Client()
    
    # Get counselor user
    counselor_user = User.objects.get(username='counselor')
    
    # Login as counselor
    login_success = client.login(username='counselor', password='password123')
    print(f"✓ Counselor login: {login_success}")
    
    if not login_success:
        print("❌ Failed to login as counselor!")
        return False
    
    # Test counselor views
    views_to_test = [
        ('counselor_dashboard', 'Counselor Dashboard'),
        ('counselor_students', 'My Students'),
        ('counselor_student_progress', 'Student Progress'),
        ('counselor_recommendations', 'Course Recommendations'),
        ('counselor_feedback', 'Student Feedback'),
        ('counselor_interventions', 'Academic Interventions'),
        ('counselor_reports', 'Reports & Analytics'),
    ]
    
    for view_name, description in views_to_test:
        try:
            url = reverse(view_name)
            response = client.get(url)
            
            if response.status_code == 200:
                print(f"✓ {description}: OK (200)")
            else:
                print(f"❌ {description}: Error {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"   Content preview: {response.content[:200]}")
                    
        except Exception as e:
            print(f"❌ {description}: Exception - {e}")
    
    # Test student detail view (if we have students)
    from course_matcher.models import CounselorStudentAssignment
    assignments = CounselorStudentAssignment.objects.filter(
        counselor=counselor_user.guidancecounselorprofile,
        is_active=True
    ).first()
    
    if assignments:
        try:
            url = reverse('counselor_student_detail', args=[assignments.student.id])
            response = client.get(url)
            
            if response.status_code == 200:
                print(f"✓ Student Detail View: OK (200)")
            else:
                print(f"❌ Student Detail View: Error {response.status_code}")
        except Exception as e:
            print(f"❌ Student Detail View: Exception - {e}")
    
    return True

def test_recommendation_review():
    """Test recommendation review functionality"""
    print("\n=== Testing Recommendation Review ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    from course_matcher.models import Recommendation
    
    # Get a recommendation to test with
    recommendation = Recommendation.objects.first()
    
    if recommendation:
        try:
            url = reverse('counselor_recommendation_review', args=[recommendation.id])
            response = client.get(url)
            
            if response.status_code == 200:
                print(f"✓ Recommendation Review View: OK (200)")
            else:
                print(f"❌ Recommendation Review View: Error {response.status_code}")
                
        except Exception as e:
            print(f"❌ Recommendation Review View: Exception - {e}")
    else:
        print("⚠️ No recommendations found to test with")
    
    return True

def test_feedback_creation():
    """Test feedback creation functionality"""
    print("\n=== Testing Feedback Creation ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    from course_matcher.models import CounselorStudentAssignment
    
    # Get a student to test with
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if assignment:
        try:
            url = reverse('counselor_feedback_create', args=[assignment.student.id])
            response = client.get(url)
            
            if response.status_code == 200:
                print(f"✓ Feedback Creation View: OK (200)")
            else:
                print(f"❌ Feedback Creation View: Error {response.status_code}")
                
        except Exception as e:
            print(f"❌ Feedback Creation View: Exception - {e}")
    else:
        print("⚠️ No student assignments found to test with")
    
    return True

def main():
    """Run all view tests"""
    print("CourseRec Counselor Views Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_counselor_views()
        success &= test_recommendation_review()
        success &= test_feedback_creation()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All view tests completed!")
            print("Check the results above for any specific issues.")
        else:
            print("❌ Some view tests failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
