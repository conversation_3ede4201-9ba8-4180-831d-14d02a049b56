#!/usr/bin/env python
"""
Test the intervention tracking system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from course_matcher.models import (
    CounselorStudentAssignment, StudentProfile, AdmissionTestAttempt
)
from course_matcher.views import get_profile_completion_status

def test_intervention_detection():
    """Test the automatic intervention detection logic"""
    print("=== Testing Intervention Detection ===")
    
    # Get counselor's assigned students
    counselor_user = User.objects.get(username='counselor')
    counselor = counselor_user.guidancecounselorprofile
    
    assigned_students = CounselorStudentAssignment.objects.filter(
        counselor=counselor,
        is_active=True
    ).select_related('student__user', 'student__major')
    
    print(f"✓ Testing with {assigned_students.count()} assigned students")
    
    students_needing_intervention = []
    
    for assignment in assigned_students:
        student = assignment.student
        progress = get_profile_completion_status(student)
        completed_count = sum(progress.values())
        
        # Test intervention criteria
        needs_intervention = False
        intervention_reasons = []
        
        # Criterion 1: Incomplete profile
        if completed_count < 3:
            needs_intervention = True
            intervention_reasons.append("Incomplete profile")
        
        # Criterion 2: Low GPA
        if student.gpa and float(student.gpa) < 2.5:
            needs_intervention = True
            intervention_reasons.append("Low GPA")
        
        # Criterion 3: Failed admission tests
        failed_tests = AdmissionTestAttempt.objects.filter(
            student=student,
            is_completed=True,
            percentage_score__lt=60
        ).count()
        
        if failed_tests > 0:
            needs_intervention = True
            intervention_reasons.append("Failed admission test(s)")
        
        if needs_intervention:
            students_needing_intervention.append({
                'student': student,
                'reasons': intervention_reasons,
                'urgency': 'high' if len(intervention_reasons) > 1 else 'medium'
            })
            
        print(f"  - {student.user.get_full_name()}: {completed_count}/6 complete, GPA: {student.gpa}, Needs intervention: {needs_intervention}")
        if intervention_reasons:
            print(f"    Reasons: {', '.join(intervention_reasons)}")
    
    print(f"✓ Found {len(students_needing_intervention)} students needing intervention")
    return students_needing_intervention

def test_intervention_page():
    """Test the intervention tracking page"""
    print("\n=== Testing Intervention Page ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test the interventions page
    url = reverse('counselor_interventions')
    response = client.get(url)
    
    if response.status_code == 200:
        print("✓ Interventions page loads successfully")
        
        # Check if the page contains intervention-related content
        content_checks = [
            (b'Academic Interventions', 'Page title'),
            (b'Immediate Attention Required', 'Alert section'),
            (b'Intervention Triggers', 'Triggers section'),
            (b'Profile Completion Status', 'Progress section'),
        ]
        
        for content, description in content_checks:
            if content in response.content:
                print(f"✓ {description} found on page")
            else:
                print(f"❌ {description} not found on page")
                
    else:
        print(f"❌ Interventions page failed: {response.status_code}")
        return False
    
    return True

def test_intervention_actions():
    """Test intervention action functionality"""
    print("\n=== Testing Intervention Actions ===")
    
    # This tests the JavaScript functionality that would be triggered
    # by the intervention action buttons
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    url = reverse('counselor_interventions')
    response = client.get(url)
    
    if response.status_code == 200:
        # Check for intervention action buttons
        action_checks = [
            (b'Send Urgent Feedback', 'Urgent feedback button'),
            (b'Emergency Meeting', 'Emergency meeting button'),
            (b'Refer to Tutoring', 'Tutoring referral button'),
            (b'Contact Parents', 'Parent contact button'),
        ]
        
        for action, description in action_checks:
            if action in response.content:
                print(f"✓ {description} found")
            else:
                print(f"❌ {description} not found")
    
    return True

def create_test_intervention_scenarios():
    """Create test scenarios for intervention detection"""
    print("\n=== Creating Test Intervention Scenarios ===")
    
    # Get a student to modify for testing
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if assignment:
        student = assignment.student
        original_gpa = student.gpa
        
        # Test scenario 1: Low GPA
        student.gpa = 2.2  # Below 2.5 threshold
        student.save()
        print(f"✓ Set {student.user.get_full_name()} GPA to {student.gpa} (low GPA scenario)")
        
        # Test the intervention detection
        students_needing_intervention = test_intervention_detection()
        
        # Check if this student is now flagged
        flagged_student = next(
            (item for item in students_needing_intervention 
             if item['student'].id == student.id), 
            None
        )
        
        if flagged_student and 'Low GPA' in flagged_student['reasons']:
            print("✓ Low GPA intervention detection working correctly")
        else:
            print("❌ Low GPA intervention detection not working")
        
        # Restore original GPA
        student.gpa = original_gpa
        student.save()
        print(f"✓ Restored {student.user.get_full_name()} GPA to {student.gpa}")
        
        return True
    else:
        print("❌ No student assignments found for testing")
        return False

def main():
    """Run all intervention system tests"""
    print("CourseRec Intervention System Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_intervention_page()
        success &= test_intervention_actions()
        students_needing_intervention = test_intervention_detection()
        success &= create_test_intervention_scenarios()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All intervention system tests passed!")
            print("The intervention tracking system is working correctly.")
            if students_needing_intervention:
                print(f"Currently tracking {len(students_needing_intervention)} students needing intervention.")
        else:
            print("❌ Some intervention system tests failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
