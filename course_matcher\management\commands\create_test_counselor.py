from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from course_matcher.models import (
    GuidanceCounselorProfile, StudentProfile, CounselorStudentAssignment, 
    Department
)
import random


class Command(BaseCommand):
    help = 'Create a test guidance counselor with assigned students'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='counselor',
            help='Username for the counselor (default: counselor)'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='password123',
            help='Password for the counselor (default: password123)'
        )
        parser.add_argument(
            '--assign-students',
            type=int,
            default=5,
            help='Number of students to assign to the counselor (default: 5)'
        )

    def handle(self, *args, **options):
        username = options['username']
        password = options['password']
        assign_count = options['assign_students']

        # Create or get the counselor user
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'first_name': 'Dr. <PERSON>',
                'last_name': '<PERSON>',
                'email': f'{username}@courserec.edu',
                'is_staff': False,
                'is_superuser': False,
            }
        )

        if created:
            user.set_password(password)
            user.save()
            self.stdout.write(f'Created user: {username}')
        else:
            self.stdout.write(f'User {username} already exists')

        # Create or get the counselor profile
        counselor_profile, created = GuidanceCounselorProfile.objects.get_or_create(
            user=user,
            defaults={
                'employee_id': f'COUNS{random.randint(1000, 9999)}',
                'specialization': 'academic',
                'phone_number': '555-0123',
                'office_location': 'Student Services Building, Room 201',
                'office_hours': 'Monday-Friday 9:00 AM - 5:00 PM',
                'bio': 'Dr. Sarah Johnson is an experienced academic counselor with over 10 years of experience helping students achieve their educational goals.',
                'years_experience': 10,
                'is_active': True,
            }
        )

        if created:
            self.stdout.write(f'Created counselor profile for {user.get_full_name()}')
            
            # Assign departments
            departments = Department.objects.all()[:3]  # Assign first 3 departments
            counselor_profile.departments.set(departments)
            self.stdout.write(f'Assigned {departments.count()} departments to counselor')
        else:
            self.stdout.write(f'Counselor profile already exists for {user.get_full_name()}')

        # Assign students to the counselor
        if assign_count > 0:
            # Get students that aren't already assigned to this counselor
            assigned_student_ids = CounselorStudentAssignment.objects.filter(
                counselor=counselor_profile,
                is_active=True
            ).values_list('student_id', flat=True)
            
            available_students = StudentProfile.objects.exclude(
                id__in=assigned_student_ids
            )[:assign_count]

            assignments_created = 0
            for student in available_students:
                assignment, created = CounselorStudentAssignment.objects.get_or_create(
                    counselor=counselor_profile,
                    student=student,
                    defaults={
                        'is_active': True,
                        'notes': f'Assigned for academic guidance in {student.major.name if student.major else "general studies"}'
                    }
                )
                if created:
                    assignments_created += 1

            self.stdout.write(f'Created {assignments_created} new student assignments')
            total_assigned = CounselorStudentAssignment.objects.filter(
                counselor=counselor_profile,
                is_active=True
            ).count()
            self.stdout.write(f'Total students assigned to counselor: {total_assigned}')

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Test counselor setup complete!\n'
                f'Username: {username}\n'
                f'Password: {password}\n'
                f'Login URL: /accounts/login/\n'
                f'Dashboard URL: /management/counselor/dashboard/\n'
            )
        )
