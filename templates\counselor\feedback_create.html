{% extends 'base.html' %}

{% block title %}Send Feedback - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Send Feedback</h1>
            <p class="text-gray-600">Provide guidance to {{ student.user.get_full_name }}</p>
        </div>
        <div class="flex items-center space-x-4">
            <a href="{% url 'counselor_student_detail' student.id %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Student
            </a>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Feedback Form -->
    <div class="lg:col-span-2">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">Feedback Details</h2>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Type and Priority Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.feedback_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Feedback Type <span class="text-red-500">*</span>
                        </label>
                        {{ form.feedback_type }}
                        {% if form.feedback_type.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.feedback_type.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Priority Level <span class="text-red-500">*</span>
                        </label>
                        {{ form.priority }}
                        {% if form.priority.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.priority.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Title -->
                <div>
                    <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Title <span class="text-red-500">*</span>
                    </label>
                    {{ form.title }}
                    {% if form.title.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Content -->
                <div>
                    <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Feedback Content <span class="text-red-500">*</span>
                    </label>
                    {{ form.content }}
                    {% if form.content.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.content.errors.0 }}</div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">Provide detailed, constructive feedback for the student.</p>
                </div>

                <!-- Recommendations -->
                <div>
                    <label for="{{ form.recommendations_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Specific Recommendations
                    </label>
                    {{ form.recommendations_text }}
                    {% if form.recommendations_text.errors %}
                        <div class="mt-1 text-sm text-red-600">{{ form.recommendations_text.errors.0 }}</div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.recommendations_text.help_text }}</p>
                </div>

                <!-- Follow-up Section -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Follow-up Settings</h3>
                    
                    <div class="space-y-4">
                        <!-- Follow-up Required Checkbox -->
                        <div class="flex items-center">
                            {{ form.follow_up_required }}
                            <label for="{{ form.follow_up_required.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">
                                Follow-up required
                            </label>
                        </div>
                        
                        <!-- Follow-up Date -->
                        <div id="follow-up-date-section" style="display: none;">
                            <label for="{{ form.follow_up_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Follow-up Date
                            </label>
                            {{ form.follow_up_date }}
                            {% if form.follow_up_date.errors %}
                                <div class="mt-1 text-sm text-red-600">{{ form.follow_up_date.errors.0 }}</div>
                            {% endif %}
                            <p class="mt-1 text-sm text-gray-500">When should you follow up on this feedback?</p>
                        </div>
                    </div>
                </div>

                <!-- Form Errors -->
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="text-sm text-red-600">
                            {{ form.non_field_errors }}
                        </div>
                    </div>
                {% endif %}

                <!-- Submit Buttons -->
                <div class="flex space-x-4 pt-6">
                    <button type="submit" 
                            class="flex-1 inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                        </svg>
                        Send Feedback
                    </button>
                    <a href="{% url 'counselor_student_detail' student.id %}" 
                       class="flex-1 inline-flex justify-center items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Student Context & Recent Feedback -->
    <div class="space-y-6">
        <!-- Student Info -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Student Information</h3>
            
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    {{ student.user.first_name|slice:":1" }}{{ student.user.last_name|slice:":1" }}
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-gray-900">{{ student.user.get_full_name }}</h4>
                    <p class="text-sm text-gray-600">{{ student.major.name|default:"No major selected" }}</p>
                </div>
            </div>
            
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Year:</span>
                    <span class="font-medium">{{ student.get_year_display }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">GPA:</span>
                    <span class="font-medium">{{ student.gpa|default:"N/A" }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Credits:</span>
                    <span class="font-medium">{{ student.total_credits }}</span>
                </div>
            </div>
            
            <!-- Profile Completion -->
            <div class="mt-4 pt-4 border-t border-gray-200">
                <h5 class="font-semibold text-gray-800 mb-2">Profile Completion</h5>
                <div class="grid grid-cols-2 gap-1">
                    {% for component, completed in progress.items %}
                        <div class="text-xs p-1 rounded text-center {% if completed %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {% if component == 'academic_records' %}📚 Records
                            {% elif component == 'interests' %}🎯 Interests
                            {% elif component == 'career_goals' %}💼 Goals
                            {% elif component == 'major' %}🎓 Major
                            {% elif component == 'admission_test' %}📝 Test
                            {% elif component == 'survey' %}📋 Survey
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Recent Feedback -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Feedback</h3>
            
            {% if recent_feedback %}
                <div class="space-y-3">
                    {% for feedback in recent_feedback %}
                    <div class="border-l-4 border-purple-500 pl-3 py-2">
                        <h5 class="font-medium text-gray-900 text-sm">{{ feedback.title }}</h5>
                        <p class="text-xs text-gray-600 mt-1">{{ feedback.get_feedback_type_display }} • {{ feedback.created_at|date:"M d" }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ feedback.content|truncatewords:15 }}</p>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-sm text-gray-500">No previous feedback sent to this student.</p>
            {% endif %}
        </div>

        <!-- Quick Templates -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Templates</h3>
            
            <div class="space-y-2">
                <button onclick="insertTemplate('academic')" 
                        class="w-full text-left p-2 rounded bg-blue-50 hover:bg-blue-100 transition-colors text-sm">
                    📚 Academic Performance
                </button>
                <button onclick="insertTemplate('career')" 
                        class="w-full text-left p-2 rounded bg-green-50 hover:bg-green-100 transition-colors text-sm">
                    💼 Career Guidance
                </button>
                <button onclick="insertTemplate('encouragement')" 
                        class="w-full text-left p-2 rounded bg-purple-50 hover:bg-purple-100 transition-colors text-sm">
                    ⭐ Encouragement
                </button>
                <button onclick="insertTemplate('intervention')" 
                        class="w-full text-left p-2 rounded bg-red-50 hover:bg-red-100 transition-colors text-sm">
                    🚨 Academic Intervention
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide follow-up date based on checkbox
document.getElementById('{{ form.follow_up_required.id_for_label }}').addEventListener('change', function() {
    const followUpSection = document.getElementById('follow-up-date-section');
    if (this.checked) {
        followUpSection.style.display = 'block';
    } else {
        followUpSection.style.display = 'none';
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    const followUpCheckbox = document.getElementById('{{ form.follow_up_required.id_for_label }}');
    const followUpSection = document.getElementById('follow-up-date-section');
    if (followUpCheckbox.checked) {
        followUpSection.style.display = 'block';
    }
});

// Template insertion
function insertTemplate(type) {
    const contentField = document.getElementById('{{ form.content.id_for_label }}');
    const titleField = document.getElementById('{{ form.title.id_for_label }}');
    const recommendationsField = document.getElementById('{{ form.recommendations_text.id_for_label }}');
    
    let title, content, recommendations;
    
    switch(type) {
        case 'academic':
            title = 'Academic Performance Review';
            content = 'I wanted to discuss your recent academic performance and provide some guidance to help you succeed in your studies.\n\nBased on my review of your progress, I have some observations and suggestions that I believe will be beneficial for your academic journey.';
            recommendations = 'Review study habits and time management\nConsider tutoring resources if needed\nMeet with professors during office hours\nUtilize academic support services';
            break;
        case 'career':
            title = 'Career Development Guidance';
            content = 'I hope this message finds you well. I wanted to reach out to discuss your career goals and provide some guidance on steps you can take to achieve them.\n\nCareer planning is an important part of your academic journey, and I\'m here to support you in making informed decisions.';
            recommendations = 'Explore internship opportunities\nAttend career fairs and networking events\nUpdate your resume and LinkedIn profile\nSchedule informational interviews in your field of interest';
            break;
        case 'encouragement':
            title = 'Encouragement and Support';
            content = 'I wanted to take a moment to acknowledge your hard work and dedication to your studies. Your commitment to your education is commendable.\n\nRemember that challenges are a normal part of the learning process, and I believe in your ability to overcome them and succeed.';
            recommendations = 'Continue your excellent work\nDon\'t hesitate to reach out for support when needed\nCelebrate your achievements along the way\nMaintain a healthy work-life balance';
            break;
        case 'intervention':
            title = 'Academic Intervention - Immediate Attention Required';
            content = 'I am reaching out because I have concerns about your current academic standing that require immediate attention and action.\n\nIt\'s important that we work together to address these challenges and get you back on track for academic success.';
            recommendations = 'Schedule an immediate meeting to discuss your situation\nDevelop an academic improvement plan\nConnect with tutoring and support services\nConsider adjusting your course load if necessary';
            break;
    }
    
    titleField.value = title;
    contentField.value = content;
    recommendationsField.value = recommendations;
}
</script>
{% endblock %}
