#!/usr/bin/env python
"""
Test for template and URL issues
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse, NoReverseMatch
from django.template import TemplateDoesNotExist
from course_matcher.models import CounselorStudentAssignment, Recommendation

def test_all_counselor_urls():
    """Test all counselor URLs for accessibility"""
    print("=== Testing All Counselor URLs ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # List of all counselor URLs to test
    url_tests = [
        ('counselor_dashboard', [], 'Dashboard'),
        ('counselor_students', [], 'Students List'),
        ('counselor_student_progress', [], 'Student Progress'),
        ('counselor_recommendations', [], 'Recommendations'),
        ('counselor_feedback', [], 'Feedback'),
        ('counselor_interventions', [], 'Interventions'),
        ('counselor_reports', [], 'Reports'),
    ]
    
    # Test URLs with parameters
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if assignment:
        student_id = assignment.student.id
        url_tests.extend([
            ('counselor_student_detail', [student_id], 'Student Detail'),
            ('counselor_feedback_create', [student_id], 'Feedback Create'),
        ])
    
    recommendation = Recommendation.objects.filter(
        student__in=CounselorStudentAssignment.objects.filter(
            counselor__user__username='counselor',
            is_active=True
        ).values_list('student', flat=True)
    ).first()
    
    if recommendation:
        url_tests.append(
            ('counselor_recommendation_review', [recommendation.id], 'Recommendation Review')
        )
    
    success_count = 0
    total_count = len(url_tests)
    
    for url_name, args, description in url_tests:
        try:
            url = reverse(url_name, args=args)
            response = client.get(url)
            
            if response.status_code == 200:
                print(f"✓ {description}: OK (200)")
                success_count += 1
            elif response.status_code == 302:
                print(f"⚠️ {description}: Redirect (302)")
                success_count += 1
            else:
                print(f"❌ {description}: Error {response.status_code}")
                
        except NoReverseMatch as e:
            print(f"❌ {description}: URL reverse error - {e}")
        except Exception as e:
            print(f"❌ {description}: Exception - {e}")
    
    print(f"\n✓ URL Test Results: {success_count}/{total_count} URLs working correctly")
    return success_count == total_count

def test_template_rendering():
    """Test template rendering for errors"""
    print("\n=== Testing Template Rendering ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test each template for rendering errors
    template_tests = [
        ('counselor_dashboard', [], 'Dashboard Template'),
        ('counselor_students', [], 'Students Template'),
        ('counselor_recommendations', [], 'Recommendations Template'),
        ('counselor_feedback', [], 'Feedback Template'),
        ('counselor_interventions', [], 'Interventions Template'),
        ('counselor_reports', [], 'Reports Template'),
    ]
    
    success_count = 0
    total_count = len(template_tests)
    
    for url_name, args, description in template_tests:
        try:
            url = reverse(url_name, args=args)
            response = client.get(url)
            
            # Check for common template errors
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for template error indicators
                error_indicators = [
                    'TemplateDoesNotExist',
                    'TemplateSyntaxError',
                    'VariableDoesNotExist',
                    'NoReverseMatch',
                    'AttributeError',
                ]
                
                has_errors = any(error in content for error in error_indicators)
                
                if not has_errors:
                    print(f"✓ {description}: Renders correctly")
                    success_count += 1
                else:
                    print(f"❌ {description}: Contains template errors")
                    for error in error_indicators:
                        if error in content:
                            print(f"   - Found: {error}")
            else:
                print(f"❌ {description}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {description}: Exception - {e}")
    
    print(f"\n✓ Template Test Results: {success_count}/{total_count} templates rendering correctly")
    return success_count == total_count

def test_static_files():
    """Test static file loading"""
    print("\n=== Testing Static Files ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test a page that uses static files
    url = reverse('counselor_dashboard')
    response = client.get(url)
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for common static file references
        static_checks = [
            ('tailwindcss', 'Tailwind CSS'),
            ('alpinejs', 'Alpine.js'),
            ('backdrop-blur', 'Glassmorphism CSS'),
        ]
        
        for check, description in static_checks:
            if check in content:
                print(f"✓ {description}: Found in template")
            else:
                print(f"❌ {description}: Not found in template")
    
    return True

def test_navigation_links():
    """Test navigation links between pages"""
    print("\n=== Testing Navigation Links ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test dashboard navigation
    url = reverse('counselor_dashboard')
    response = client.get(url)
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for navigation links
        nav_checks = [
            ('counselor_students', 'Students link'),
            ('counselor_recommendations', 'Recommendations link'),
            ('counselor_feedback', 'Feedback link'),
            ('counselor_interventions', 'Interventions link'),
            ('counselor_reports', 'Reports link'),
        ]
        
        for url_name, description in nav_checks:
            try:
                expected_url = reverse(url_name)
                if expected_url in content:
                    print(f"✓ {description}: Found in navigation")
                else:
                    print(f"❌ {description}: Not found in navigation")
            except NoReverseMatch:
                print(f"❌ {description}: URL reverse error")
    
    return True

def test_form_csrf_tokens():
    """Test CSRF tokens in forms"""
    print("\n=== Testing CSRF Tokens ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test pages with forms
    form_pages = [
        ('counselor_feedback_create', 'Feedback Creation Form'),
        ('counselor_recommendations', 'Recommendation Filters'),
    ]
    
    assignment = CounselorStudentAssignment.objects.filter(
        counselor__user__username='counselor',
        is_active=True
    ).first()
    
    if assignment:
        for url_name, description in form_pages:
            try:
                if url_name == 'counselor_feedback_create':
                    url = reverse(url_name, args=[assignment.student.id])
                else:
                    url = reverse(url_name)
                    
                response = client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    
                    if 'csrfmiddlewaretoken' in content:
                        print(f"✓ {description}: CSRF token found")
                    else:
                        print(f"❌ {description}: CSRF token missing")
                        
            except Exception as e:
                print(f"❌ {description}: Exception - {e}")
    
    return True

def main():
    """Run all template and URL tests"""
    print("CourseRec Template & URL Issues Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_all_counselor_urls()
        success &= test_template_rendering()
        success &= test_static_files()
        success &= test_navigation_links()
        success &= test_form_csrf_tokens()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All template and URL tests passed!")
            print("No template or URL issues found.")
        else:
            print("❌ Some template or URL issues found!")
            print("Check the results above for specific problems.")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
