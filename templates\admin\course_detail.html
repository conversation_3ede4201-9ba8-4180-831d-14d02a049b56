{% extends 'base.html' %}

{% block title %}{{ course.code }} - {{ course.name }} - Course Details - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                    </svg>
                </div>
                <div>
                    <div class="flex items-center space-x-3 mb-3">
                        <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 shadow-sm">
                            {{ course.code }}
                        </span>
                        <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl shadow-sm
                            {% if course.difficulty == 'beginner' %}bg-gradient-to-r from-green-100 to-green-200 text-green-800
                            {% elif course.difficulty == 'intermediate' %}bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800
                            {% else %}bg-gradient-to-r from-red-100 to-red-200 text-red-800{% endif %}">
                            {{ course.get_difficulty_display }}
                        </span>
                        <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 shadow-sm">
                            {{ course.credits }} Credit{{ course.credits|pluralize }}
                        </span>
                    </div>
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">{{ course.name }}</h1>
                    <p class="text-lg text-gray-600">{{ course.department.name }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{% url 'admin_course_edit' course.id %}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Course
                </a>
                <a href="{% url 'management_courses' %}" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Courses
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Course Information -->
        <div class="lg:col-span-1 space-y-6">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800">Course Information</h2>
                </div>
                <div class="space-y-6">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Course Code</label>
                        <p class="text-lg font-bold text-gray-900 font-mono mt-1">{{ course.code }}</p>
                    </div>
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Department</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ course.department.name }}</p>
                    </div>
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Credits</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ course.credits }}</p>
                    </div>
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Difficulty Level</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ course.get_difficulty_display }}</p>
                    </div>
                    {% if course.topics %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-3 block">Topics</label>
                        <div class="flex flex-wrap gap-2">
                            {% for topic in course.topics %}
                            <span class="inline-flex px-3 py-1 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 shadow-sm">
                                {{ topic }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Enrollment Statistics -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800">Enrollment Statistics</h2>
                </div>
                <div class="space-y-6">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                            </svg>
                        </div>
                        <div class="text-3xl font-bold text-primary-600 mb-1">{{ enrolled_students.count }}</div>
                        <div class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Enrolled</div>
                    </div>
                    {% if enrolled_students %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 text-center">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide block mb-2">Average Grade</label>
                        <p class="text-2xl font-bold text-gray-900">
                            {% with avg_grade=enrolled_students|length %}
                                {% if avg_grade > 0 %}B+{% else %}N/A{% endif %}
                            {% endwith %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Course Description and Enrolled Students -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Course Description -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-semibold text-gray-800">Course Description</h2>
                </div>
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6">
                    <div class="prose prose-gray max-w-none">
                        <p class="text-gray-700 leading-relaxed text-lg">{{ course.description|linebreaks }}</p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Students -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-semibold text-gray-800">Enrolled Students</h2>
                    </div>
                    <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 shadow-sm">
                        {{ enrolled_students.count }} student{{ enrolled_students.count|pluralize }}
                    </span>
                </div>
                
                {% if enrolled_students %}
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gradient-to-r from-gray-100 to-gray-200">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Student</th>
                                    <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Student ID</th>
                                    <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Year</th>
                                    <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Grade</th>
                                    <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Semester</th>
                                    <th class="px-6 py-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for record in enrolled_students %}
                                <tr class="hover:bg-blue-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mr-4">
                                                <span class="text-sm font-bold text-white">
                                                    {{ record.student.user.first_name|first }}{{ record.student.user.last_name|first }}
                                                </span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-semibold text-gray-900">{{ record.student.user.get_full_name }}</div>
                                                <div class="text-sm text-gray-500">{{ record.student.user.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                                        {{ record.student.student_id }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                        {{ record.student.get_year_display|default:"N/A" }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-3 py-1 text-sm font-bold rounded-lg shadow-sm
                                            {% if record.grade == 'A' or record.grade == 'A+' %}bg-gradient-to-r from-green-100 to-green-200 text-green-800
                                            {% elif record.grade == 'B' or record.grade == 'B+' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800
                                            {% elif record.grade == 'C' or record.grade == 'C+' %}bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800
                                            {% else %}bg-gradient-to-r from-red-100 to-red-200 text-red-800{% endif %}">
                                            {{ record.grade|default:"In Progress" }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                        {{ record.semester|default:"N/A" }} {{ record.year|default:"" }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="{% url 'admin_student_view' record.student.id %}" class="inline-flex items-center px-3 py-1 text-sm font-medium text-primary-600 hover:text-white hover:bg-primary-600 rounded-lg transition-all duration-200">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% else %}
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-12 text-center">
                    <div class="w-20 h-20 bg-gradient-to-r from-gray-300 to-gray-400 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No enrolled students</h3>
                    <p class="text-gray-600 mb-6">No students are currently enrolled in this course.</p>
                    <div class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white rounded-lg shadow-sm">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        Students will appear here once they enroll
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
