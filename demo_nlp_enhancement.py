#!/usr/bin/env python
"""
Demo script to showcase the NLP enhancement features
Run this script to see the NLP-enhanced recommendation system in action
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.contrib.auth.models import User
from course_matcher.models import StudentProfile, Course, Department, AcademicRecord
from course_matcher.recommendation_service import RecommendationEngine
from course_matcher.nlp_service import GeminiNLPService


def create_demo_data():
    """Create demo data for testing"""
    print("🔧 Setting up demo data...")
    
    # Create department
    dept, created = Department.objects.get_or_create(
        code="CS",
        defaults={
            "name": "Computer Science",
            "description": "Department of Computer Science and Engineering"
        }
    )
    
    # Create demo courses
    courses_data = [
        {
            "code": "CS301",
            "name": "Machine Learning Fundamentals",
            "description": "Introduction to machine learning algorithms, supervised and unsupervised learning, neural networks, and practical applications in data science and artificial intelligence.",
            "topics": ["Machine Learning", "Neural Networks", "Data Science", "Python", "Algorithms"],
            "difficulty": "intermediate"
        },
        {
            "code": "CS401", 
            "name": "Deep Learning and AI",
            "description": "Advanced course covering deep neural networks, convolutional networks, recurrent networks, and modern AI applications including computer vision and natural language processing.",
            "topics": ["Deep Learning", "AI", "Computer Vision", "NLP", "TensorFlow"],
            "difficulty": "advanced"
        },
        {
            "code": "CS201",
            "name": "Web Development",
            "description": "Full-stack web development using modern frameworks, databases, and deployment strategies. Covers frontend and backend technologies.",
            "topics": ["Web Development", "JavaScript", "React", "Node.js", "Databases"],
            "difficulty": "intermediate"
        }
    ]
    
    for course_data in courses_data:
        course, created = Course.objects.get_or_create(
            code=course_data["code"],
            defaults={
                "name": course_data["name"],
                "department": dept,
                "credits": 3,
                "description": course_data["description"],
                "topics": course_data["topics"],
                "difficulty": course_data["difficulty"]
            }
        )
        if created:
            print(f"  ✓ Created course: {course.code}")
    
    # Create demo student
    user, created = User.objects.get_or_create(
        username="demo_student",
        defaults={
            "email": "<EMAIL>",
            "first_name": "Demo",
            "last_name": "Student"
        }
    )
    
    if created:
        user.set_password("demo123")
        user.save()
    
    student, created = StudentProfile.objects.get_or_create(
        user=user,
        defaults={
            "student_id": "DEMO001",
            "year": "junior",
            "major": dept,
            "interests": ["Machine Learning", "Data Science", "Python Programming"],
            "career_goals": "I want to become a data scientist specializing in machine learning and AI applications in healthcare and finance.",
            "preferred_difficulty": "intermediate"
        }
    )
    
    if created:
        print(f"  ✓ Created student: {student.student_id}")
    
    return student


def demo_nlp_service():
    """Demonstrate NLP service capabilities"""
    print("\n🤖 Testing NLP Service...")
    
    try:
        nlp_service = GeminiNLPService()
        print("  ✓ NLP service initialized successfully")
        
        # Demo semantic similarity
        print("\n  📊 Semantic Similarity Analysis:")
        similarity = nlp_service.analyze_semantic_similarity(
            "Machine Learning and Data Science",
            "Introduction to machine learning algorithms and data analysis techniques",
            "academic course matching"
        )
        
        print(f"    Score: {similarity.score:.2f}")
        print(f"    Explanation: {similarity.explanation}")
        print(f"    Key Matches: {', '.join(similarity.key_matches)}")
        
        # Demo course analysis
        print("\n  📚 Course Content Analysis:")
        course = Course.objects.filter(code="CS301").first()
        if course:
            analysis = nlp_service.analyze_course_content(
                course.description,
                course.name,
                course.topics
            )
            
            print(f"    Course: {course.name}")
            print(f"    Difficulty Assessment: {analysis.difficulty_assessment}")
            print(f"    Extracted Topics: {', '.join(analysis.topics[:5])}")
            print(f"    Learning Outcomes: {len(analysis.learning_outcomes)} identified")
        
        return True
        
    except Exception as e:
        print(f"  ❌ NLP service error: {e}")
        print("  ℹ️  This is expected if GEMINI_API_KEY is not set or invalid")
        return False


def demo_enhanced_recommendations(student):
    """Demonstrate enhanced recommendation system"""
    print("\n🎯 Testing Enhanced Recommendations...")
    
    try:
        engine = RecommendationEngine()
        
        print(f"\n  👤 Student Profile:")
        print(f"    Name: {student.user.get_full_name()}")
        print(f"    Interests: {', '.join(student.interests)}")
        print(f"    Career Goals: {student.career_goals[:100]}...")
        
        print(f"\n  🔍 Generating recommendations...")
        recommendations = engine.get_recommendations(student, limit=3)
        
        print(f"\n  📋 Top {len(recommendations)} Recommendations:")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"\n    {i}. {rec.course.code} - {rec.course.name}")
            print(f"       Confidence Score: {rec.confidence_score:.2f}")
            print(f"       Recommendation Type: {rec.recommendation_type}")
            print(f"       Reasoning: {rec.reasoning[:150]}...")
            
            # Show course details
            print(f"       Course Topics: {', '.join(rec.course.topics[:3])}...")
            print(f"       Difficulty: {rec.course.difficulty}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Recommendation error: {e}")
        return False


def demo_performance_comparison():
    """Compare traditional vs NLP-enhanced recommendations"""
    print("\n⚡ Performance Comparison...")
    
    try:
        import time
        
        student = StudentProfile.objects.filter(student_id="DEMO001").first()
        if not student:
            print("  ❌ Demo student not found")
            return
        
        # Test with NLP enabled
        print("  🤖 Testing with NLP enhancement...")
        start_time = time.time()
        engine_nlp = RecommendationEngine()
        recs_nlp = engine_nlp.get_recommendations(student, limit=3)
        nlp_time = time.time() - start_time
        
        print(f"    Time with NLP: {nlp_time:.2f}s")
        print(f"    Recommendations generated: {len(recs_nlp)}")
        
        # Show the difference in reasoning quality
        if recs_nlp:
            print(f"    Sample reasoning: {recs_nlp[0].reasoning[:100]}...")
        
    except Exception as e:
        print(f"  ❌ Performance test error: {e}")


def main():
    """Main demo function"""
    print("🚀 CourseRec NLP Enhancement Demo")
    print("=" * 50)
    
    # Create demo data
    student = create_demo_data()
    
    # Test NLP service
    nlp_available = demo_nlp_service()
    
    # Test enhanced recommendations
    demo_enhanced_recommendations(student)
    
    # Performance comparison
    if nlp_available:
        demo_performance_comparison()
    
    print("\n" + "=" * 50)
    print("✅ Demo completed!")
    
    if not nlp_available:
        print("\n💡 To enable full NLP features:")
        print("   1. Set GEMINI_API_KEY in your .env file")
        print("   2. Ensure you have API quota available")
        print("   3. Run the demo again")
    else:
        print("\n🎉 NLP enhancement is working perfectly!")
        print("   • Semantic similarity analysis ✓")
        print("   • Enhanced explanations ✓") 
        print("   • Intelligent caching ✓")
        print("   • Graceful error handling ✓")


if __name__ == "__main__":
    main()
