{% extends 'base.html' %}

{% block title %}{{ student.user.get_full_name }} - Student Details - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-xl">
                {{ student.user.first_name|slice:":1" }}{{ student.user.last_name|slice:":1" }}
            </div>
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-1">{{ student.user.get_full_name }}</h1>
                <p class="text-lg text-gray-600">{{ student.major.name|default:"No major selected" }}</p>
                <div class="flex items-center space-x-4 mt-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ student.get_year_display }}
                    </span>
                    <span class="text-sm text-gray-500">Student ID: {{ student.student_id }}</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <a href="{% url 'counselor_feedback_create' student.id %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                </svg>
                Send Feedback
            </a>
            <a href="{% url 'counselor_students' %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                Back to Students
            </a>
        </div>
    </div>
</div>

<!-- Progress Overview -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <!-- Overall Progress -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="w-20 h-20 mx-auto mb-4 relative">
                <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    <path class="text-purple-600" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round" 
                          stroke-dasharray="{% widthratio completion_percentage 100 100 %}, 100" 
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-xl font-bold text-gray-900">{{ completion_percentage|floatformat:0 }}%</span>
                </div>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Profile Complete</h3>
            <p class="text-sm text-gray-600">{{ completed_count }}/6 components</p>
        </div>
    </div>

    <!-- GPA -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900">{{ student.gpa|default:"N/A" }}</h3>
            <p class="text-sm text-gray-600">Current GPA</p>
        </div>
    </div>

    <!-- Total Credits -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900">{{ student.total_credits|default:"0" }}</h3>
            <p class="text-sm text-gray-600">Credits Earned</p>
        </div>
    </div>

    <!-- Latest Test Score -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            {% if test_attempts %}
                <h3 class="text-2xl font-bold text-gray-900">{{ test_attempts.0.percentage_score|floatformat:1 }}%</h3>
                <p class="text-sm text-gray-600">Latest Test</p>
            {% else %}
                <h3 class="text-2xl font-bold text-gray-900">N/A</h3>
                <p class="text-sm text-gray-600">No Tests Taken</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Component Status Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Profile Components -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-6">Profile Components</h2>
        <div class="space-y-4">
            {% for component, completed in progress.items %}
            <div class="flex items-center justify-between p-3 rounded-lg {% if completed %}bg-green-50 border border-green-200{% else %}bg-red-50 border border-red-200{% endif %}">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {% if completed %}bg-green-500 text-white{% else %}bg-red-500 text-white{% endif %}">
                        {% if completed %}
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        {% else %}
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        {% endif %}
                    </div>
                    <span class="font-medium {% if completed %}text-green-800{% else %}text-red-800{% endif %}">
                        {% if component == 'academic_records' %}Academic Records
                        {% elif component == 'interests' %}Interests & Skills
                        {% elif component == 'career_goals' %}Career Goals
                        {% elif component == 'major' %}Major Selection
                        {% elif component == 'admission_test' %}Admission Test
                        {% elif component == 'survey' %}Learning Survey
                        {% endif %}
                    </span>
                </div>
                <span class="text-sm {% if completed %}text-green-600{% else %}text-red-600{% endif %}">
                    {% if completed %}Complete{% else %}Incomplete{% endif %}
                </span>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Recent Feedback -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-6">Counselor Feedback History</h2>
        {% if feedback_history %}
            <div class="space-y-4">
                {% for feedback in feedback_history|slice:":5" %}
                <div class="border-l-4 border-purple-500 pl-4 py-2">
                    <h4 class="font-medium text-gray-900">{{ feedback.title }}</h4>
                    <p class="text-sm text-gray-600 mt-1">{{ feedback.content|truncatewords:20 }}</p>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-xs text-gray-500">{{ feedback.created_at|date:"M d, Y" }}</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {{ feedback.get_feedback_type_display }}
                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.544-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No feedback yet</h3>
                <p class="mt-1 text-sm text-gray-500">Start providing guidance to this student.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Academic Records and Recommendations -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Academic Records -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-6">Academic Records</h2>
        {% if academic_records %}
            <div class="space-y-3">
                {% for record in academic_records|slice:":10" %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <h4 class="font-medium text-gray-900">{{ record.course.code }}</h4>
                        <p class="text-sm text-gray-600">{{ record.course.name }}</p>
                    </div>
                    <div class="text-right">
                        <span class="font-bold text-lg {% if record.grade in 'A,A-,B+,B' %}text-green-600{% elif record.grade in 'B-,C+,C' %}text-yellow-600{% else %}text-red-600{% endif %}">
                            {{ record.grade|default:"In Progress" }}
                        </span>
                        <p class="text-xs text-gray-500">{{ record.course.credits }} credits</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No academic records</h3>
                <p class="mt-1 text-sm text-gray-500">Student hasn't enrolled in any courses yet.</p>
            </div>
        {% endif %}
    </div>

    <!-- Course Recommendations -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-6">Course Recommendations</h2>
        {% if recommendations %}
            <div class="space-y-3">
                {% for rec in recommendations|slice:":5" %}
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div>
                        <h4 class="font-medium text-gray-900">{{ rec.course.code }}</h4>
                        <p class="text-sm text-gray-600">{{ rec.course.name }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ rec.reasoning|truncatewords:15 }}</p>
                    </div>
                    <div class="text-right">
                        <span class="font-bold text-lg text-blue-600">{{ rec.confidence_score|floatformat:2 }}</span>
                        <p class="text-xs text-gray-500">confidence</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No recommendations yet</h3>
                <p class="mt-1 text-sm text-gray-500">Complete profile to generate recommendations.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
