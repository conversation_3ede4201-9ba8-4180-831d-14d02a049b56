"""
Management command to test NLP integration and performance
"""
import time
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.core.cache import cache
from course_matcher.models import StudentProfile, Course, Department, AcademicRecord
from course_matcher.recommendation_service import RecommendationEngine
from course_matcher.nlp_service import GeminiNLPService


class Command(BaseCommand):
    help = 'Test NLP integration and performance'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test data for NLP testing',
        )
        parser.add_argument(
            '--test-nlp-service',
            action='store_true',
            help='Test NLP service functionality',
        )
        parser.add_argument(
            '--test-recommendations',
            action='store_true',
            help='Test enhanced recommendations',
        )
        parser.add_argument(
            '--performance-test',
            action='store_true',
            help='Run performance tests',
        )
    
    def handle(self, *args, **options):
        if options['create_test_data']:
            self.create_test_data()
        
        if options['test_nlp_service']:
            self.test_nlp_service()
        
        if options['test_recommendations']:
            self.test_recommendations()
        
        if options['performance_test']:
            self.performance_test()
        
        if not any(options.values()):
            self.stdout.write("No options specified. Use --help for available options.")
    
    def create_test_data(self):
        """Create comprehensive test data for NLP testing"""
        self.stdout.write("Creating test data...")
        
        # Create departments
        cs_dept, _ = Department.objects.get_or_create(
            code="CS",
            defaults={
                "name": "Computer Science",
                "description": "Department of Computer Science and Engineering"
            }
        )
        
        math_dept, _ = Department.objects.get_or_create(
            code="MATH",
            defaults={
                "name": "Mathematics",
                "description": "Department of Mathematics and Statistics"
            }
        )
        
        # Create diverse courses
        courses_data = [
            {
                "code": "CS301",
                "name": "Machine Learning Fundamentals",
                "department": cs_dept,
                "description": "Introduction to machine learning algorithms, supervised and unsupervised learning, neural networks, and practical applications in data science and artificial intelligence.",
                "topics": ["Machine Learning", "Neural Networks", "Data Science", "Python", "Algorithms"],
                "difficulty": "intermediate"
            },
            {
                "code": "CS401",
                "name": "Deep Learning and AI",
                "department": cs_dept,
                "description": "Advanced course covering deep neural networks, convolutional networks, recurrent networks, and modern AI applications including computer vision and natural language processing.",
                "topics": ["Deep Learning", "AI", "Computer Vision", "NLP", "TensorFlow"],
                "difficulty": "advanced"
            },
            {
                "code": "CS201",
                "name": "Web Development",
                "department": cs_dept,
                "description": "Full-stack web development using modern frameworks, databases, and deployment strategies. Covers frontend and backend technologies.",
                "topics": ["Web Development", "JavaScript", "React", "Node.js", "Databases"],
                "difficulty": "intermediate"
            },
            {
                "code": "MATH301",
                "name": "Statistics for Data Science",
                "department": math_dept,
                "description": "Statistical methods and probability theory with applications to data analysis, hypothesis testing, and machine learning.",
                "topics": ["Statistics", "Probability", "Data Analysis", "R", "Hypothesis Testing"],
                "difficulty": "intermediate"
            }
        ]
        
        for course_data in courses_data:
            course, created = Course.objects.get_or_create(
                code=course_data["code"],
                defaults={
                    "name": course_data["name"],
                    "department": course_data["department"],
                    "credits": 3,
                    "description": course_data["description"],
                    "topics": course_data["topics"],
                    "difficulty": course_data["difficulty"]
                }
            )
            if created:
                self.stdout.write(f"Created course: {course.code}")
        
        # Create test students with diverse profiles
        students_data = [
            {
                "username": "alice_ml",
                "student_id": "CS2021001",
                "interests": ["Machine Learning", "Data Science", "Python Programming"],
                "career_goals": "I want to become a data scientist specializing in machine learning and AI applications in healthcare.",
                "year": "junior"
            },
            {
                "username": "bob_web",
                "student_id": "CS2021002", 
                "interests": ["Web Development", "JavaScript", "User Experience"],
                "career_goals": "My goal is to become a full-stack web developer creating innovative web applications.",
                "year": "sophomore"
            },
            {
                "username": "carol_ai",
                "student_id": "CS2021003",
                "interests": ["Artificial Intelligence", "Computer Vision", "Deep Learning"],
                "career_goals": "I aspire to work in AI research, particularly in computer vision and autonomous systems.",
                "year": "senior"
            }
        ]
        
        for student_data in students_data:
            user, created = User.objects.get_or_create(
                username=student_data["username"],
                defaults={
                    "email": f"{student_data['username']}@university.edu",
                    "first_name": student_data["username"].split('_')[0].title(),
                    "last_name": "Student"
                }
            )
            
            if created:
                user.set_password("testpass123")
                user.save()
            
            student, created = StudentProfile.objects.get_or_create(
                user=user,
                defaults={
                    "student_id": student_data["student_id"],
                    "year": student_data["year"],
                    "major": cs_dept,
                    "interests": student_data["interests"],
                    "career_goals": student_data["career_goals"],
                    "preferred_difficulty": "intermediate"
                }
            )
            
            if created:
                self.stdout.write(f"Created student: {student.student_id}")
        
        self.stdout.write(self.style.SUCCESS("Test data created successfully!"))
    
    def test_nlp_service(self):
        """Test NLP service functionality"""
        self.stdout.write("Testing NLP service...")
        
        try:
            nlp_service = GeminiNLPService()
            self.stdout.write("✓ NLP service initialized successfully")
            
            # Test semantic similarity
            similarity = nlp_service.analyze_semantic_similarity(
                "Machine Learning and Data Science",
                "Introduction to machine learning algorithms and data analysis",
                "academic course matching"
            )
            
            self.stdout.write(f"✓ Semantic similarity test: Score = {similarity.score:.2f}")
            self.stdout.write(f"  Explanation: {similarity.explanation}")
            
            # Test course content analysis
            course = Course.objects.filter(code="CS301").first()
            if course:
                analysis = nlp_service.analyze_course_content(
                    course.description,
                    course.name,
                    course.topics
                )
                
                self.stdout.write(f"✓ Course analysis test: Difficulty = {analysis.difficulty_assessment}")
                self.stdout.write(f"  Topics: {', '.join(analysis.topics[:3])}...")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ NLP service test failed: {e}"))
    
    def test_recommendations(self):
        """Test enhanced recommendations"""
        self.stdout.write("Testing enhanced recommendations...")
        
        try:
            # Get a test student
            student = StudentProfile.objects.filter(student_id="CS2021001").first()
            if not student:
                self.stdout.write(self.style.ERROR("No test student found. Run --create-test-data first."))
                return
            
            engine = RecommendationEngine()
            
            # Generate recommendations
            start_time = time.time()
            recommendations = engine.get_recommendations(student, limit=3)
            end_time = time.time()
            
            self.stdout.write(f"✓ Generated {len(recommendations)} recommendations in {end_time - start_time:.2f}s")
            
            for i, rec in enumerate(recommendations, 1):
                self.stdout.write(f"\n{i}. {rec.course.code} - {rec.course.name}")
                self.stdout.write(f"   Confidence: {rec.confidence_score:.2f}")
                self.stdout.write(f"   Reasoning: {rec.reasoning[:100]}...")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Recommendations test failed: {e}"))
    
    def performance_test(self):
        """Run performance tests"""
        self.stdout.write("Running performance tests...")
        
        try:
            # Test caching performance
            cache.clear()
            nlp_service = GeminiNLPService()
            
            # First call (should hit API)
            start_time = time.time()
            similarity1 = nlp_service.analyze_semantic_similarity(
                "test text 1", "test text 2", "performance test"
            )
            first_call_time = time.time() - start_time
            
            # Second call (should use cache)
            start_time = time.time()
            similarity2 = nlp_service.analyze_semantic_similarity(
                "test text 1", "test text 2", "performance test"
            )
            second_call_time = time.time() - start_time
            
            self.stdout.write(f"✓ First call: {first_call_time:.2f}s")
            self.stdout.write(f"✓ Cached call: {second_call_time:.2f}s")
            self.stdout.write(f"✓ Cache speedup: {first_call_time/second_call_time:.1f}x")
            
            # Test recommendation generation performance
            students = StudentProfile.objects.all()[:3]
            total_time = 0
            
            for student in students:
                start_time = time.time()
                engine = RecommendationEngine()
                recommendations = engine.get_recommendations(student, limit=5)
                end_time = time.time()
                
                student_time = end_time - start_time
                total_time += student_time
                
                self.stdout.write(f"✓ Student {student.student_id}: {student_time:.2f}s for {len(recommendations)} recommendations")
            
            avg_time = total_time / len(students)
            self.stdout.write(f"✓ Average recommendation time: {avg_time:.2f}s per student")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Performance test failed: {e}"))
