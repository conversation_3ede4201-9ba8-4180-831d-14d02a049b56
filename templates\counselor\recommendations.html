{% extends 'base.html' %}

{% block title %}Course Recommendations Review - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Course Recommendations Review</h1>
            <p class="text-gray-600">Review and approve ML-generated course recommendations</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Status Filter -->
            <div class="relative">
                <select id="statusFilter" class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Recommendations</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending Review</option>
                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                    <option value="modified" {% if status_filter == 'modified' %}selected{% endif %}>Modified</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ total_recommendations }}</div>
            <div class="text-sm text-gray-600">Total</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">{{ pending_count }}</div>
            <div class="text-sm text-gray-600">Pending</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ approved_count }}</div>
            <div class="text-sm text-gray-600">Approved</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ modified_count }}</div>
            <div class="text-sm text-gray-600">Modified</div>
        </div>
    </div>
    <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/20">
        <div class="text-center">
            <div class="text-2xl font-bold text-red-600">{{ rejected_count }}</div>
            <div class="text-sm text-gray-600">Rejected</div>
        </div>
    </div>
</div>

<!-- Recommendations List -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
    {% if approvals %}
        <div class="divide-y divide-gray-200">
            {% for approval in approvals %}
            <div class="p-6 hover:bg-gray-50/50 transition-colors">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <!-- Student Info -->
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                                {{ approval.recommendation.student.user.first_name|slice:":1" }}{{ approval.recommendation.student.user.last_name|slice:":1" }}
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ approval.recommendation.student.user.get_full_name }}</h3>
                                <p class="text-sm text-gray-600">{{ approval.recommendation.student.major.name|default:"No major selected" }}</p>
                            </div>
                        </div>

                        <!-- Course Recommendation -->
                        <div class="bg-blue-50 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-xl font-bold text-blue-900">{{ approval.recommendation.course.code }}</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-blue-700">Confidence:</span>
                                    <span class="text-lg font-bold text-blue-900">{{ approval.recommendation.confidence_score|floatformat:2 }}</span>
                                </div>
                            </div>
                            <h5 class="text-lg font-semibold text-blue-800 mb-2">{{ approval.recommendation.course.name }}</h5>
                            <p class="text-sm text-blue-700 mb-2">{{ approval.recommendation.course.department.name }} • {{ approval.recommendation.course.credits }} credits</p>
                            <div class="bg-white/50 rounded p-3">
                                <p class="text-sm text-gray-700"><strong>ML Reasoning:</strong> {{ approval.recommendation.reasoning }}</p>
                            </div>
                        </div>

                        <!-- Status and Notes -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    {% if approval.status == 'pending' %}bg-orange-100 text-orange-800
                                    {% elif approval.status == 'approved' %}bg-green-100 text-green-800
                                    {% elif approval.status == 'modified' %}bg-blue-100 text-blue-800
                                    {% elif approval.status == 'rejected' %}bg-red-100 text-red-800
                                    {% endif %}">
                                    {{ approval.get_status_display }}
                                </span>
                                {% if approval.reviewed_at %}
                                    <span class="text-sm text-gray-500">Reviewed {{ approval.reviewed_at|timesince }} ago</span>
                                {% endif %}
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'counselor_student_detail' approval.recommendation.student.id %}"
                                   class="text-purple-600 hover:text-purple-900 text-sm font-medium">View Student</a>
                                <a href="{% url 'counselor_recommendation_review' approval.recommendation.id %}"
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
                                    {% if approval.status == 'pending' %}Review{% else %}Edit Review{% endif %}
                                </a>
                            </div>
                        </div>

                        <!-- Counselor Notes (if any) -->
                        {% if approval.counselor_notes %}
                        <div class="mt-4 bg-purple-50 rounded-lg p-3">
                            <p class="text-sm text-purple-800"><strong>Your Notes:</strong> {{ approval.counselor_notes }}</p>
                        </div>
                        {% endif %}

                        <!-- Modified Reasoning (if any) -->
                        {% if approval.modified_reasoning %}
                        <div class="mt-2 bg-blue-50 rounded-lg p-3">
                            <p class="text-sm text-blue-800"><strong>Modified Reasoning:</strong> {{ approval.modified_reasoning }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No recommendations found</h3>
            <p class="mt-1 text-sm text-gray-500">
                {% if status_filter == 'pending' %}
                    No pending recommendations to review.
                {% else %}
                    No recommendations match the current filter.
                {% endif %}
            </p>
        </div>
    {% endif %}
</div>

<script>
document.getElementById('statusFilter').addEventListener('change', function() {
    const status = this.value;
    const url = new URL(window.location);
    if (status === 'all') {
        url.searchParams.delete('status');
    } else {
        url.searchParams.set('status', status);
    }
    window.location.href = url.toString();
});
</script>
{% endblock %}
