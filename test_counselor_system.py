#!/usr/bin/env python
"""
Test script for the guidance counselor dashboard system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.contrib.auth.models import User
from course_matcher.models import (
    GuidanceCounselorProfile, CounselorStudentAssignment, 
    StudentProfile, CounselorFeedback, Recommendation, RecommendationApproval
)

def test_counselor_setup():
    """Test if the counselor account is properly set up"""
    print("=== Testing Counselor Setup ===")
    
    # Check counselor user
    counselor_user = User.objects.filter(username='counselor').first()
    print(f"✓ Counselor user exists: {counselor_user is not None}")
    
    if not counselor_user:
        print("❌ Counselor user not found!")
        return False
    
    print(f"  - Username: {counselor_user.username}")
    print(f"  - Full name: {counselor_user.get_full_name()}")
    print(f"  - Email: {counselor_user.email}")
    
    # Check counselor profile
    try:
        counselor_profile = counselor_user.guidancecounselorprofile
        print(f"✓ Counselor profile exists: {counselor_profile is not None}")
        print(f"  - Employee ID: {counselor_profile.employee_id}")
        print(f"  - Specialization: {counselor_profile.get_specialization_display()}")
        print(f"  - Is active: {counselor_profile.is_active}")
        print(f"  - Years experience: {counselor_profile.years_experience}")
    except GuidanceCounselorProfile.DoesNotExist:
        print("❌ Counselor profile not found!")
        return False
    
    # Check student assignments
    assignments = CounselorStudentAssignment.objects.filter(
        counselor=counselor_profile,
        is_active=True
    )
    print(f"✓ Student assignments: {assignments.count()}")
    
    for assignment in assignments[:3]:  # Show first 3
        print(f"  - {assignment.student.user.get_full_name()}")
    
    return True

def test_student_data():
    """Test if there are students with proper data"""
    print("\n=== Testing Student Data ===")
    
    students = StudentProfile.objects.all()
    print(f"✓ Total students: {students.count()}")
    
    if students.count() == 0:
        print("❌ No students found!")
        return False
    
    # Check students with different completion levels
    complete_profiles = 0
    partial_profiles = 0
    minimal_profiles = 0
    
    for student in students[:5]:  # Check first 5 students
        # This would need the get_profile_completion_status function
        # For now, just check basic data
        has_major = student.major is not None
        has_gpa = student.gpa is not None
        print(f"  - {student.user.get_full_name()}: Major={has_major}, GPA={has_gpa}")
    
    return True

def test_recommendations():
    """Test if there are recommendations to review"""
    print("\n=== Testing Recommendations ===")
    
    recommendations = Recommendation.objects.all()
    print(f"✓ Total recommendations: {recommendations.count()}")
    
    if recommendations.count() > 0:
        # Check if there are any approval records
        approvals = RecommendationApproval.objects.all()
        print(f"✓ Recommendation approvals: {approvals.count()}")
        
        # Show some sample recommendations
        for rec in recommendations[:3]:
            print(f"  - {rec.student.user.get_full_name()}: {rec.course.code} (confidence: {rec.confidence_score})")
    
    return True

def test_feedback_system():
    """Test the feedback system"""
    print("\n=== Testing Feedback System ===")
    
    feedback_count = CounselorFeedback.objects.all().count()
    print(f"✓ Total feedback records: {feedback_count}")
    
    return True

def main():
    """Run all tests"""
    print("CourseRec Guidance Counselor System Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_counselor_setup()
        success &= test_student_data()
        success &= test_recommendations()
        success &= test_feedback_system()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All basic tests passed!")
            print("The counselor system appears to be set up correctly.")
        else:
            print("❌ Some tests failed!")
            print("Please check the issues above.")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
