#!/usr/bin/env python
"""
Test the complete recommendation workflow
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from course_matcher.models import (
    Recommendation, RecommendationApproval, CounselorFeedback
)

def test_recommendation_workflow():
    """Test the complete recommendation approval workflow"""
    print("=== Testing Recommendation Workflow ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Get a recommendation to test with
    recommendation = Recommendation.objects.get(id=23)  # From previous test
    print(f"✓ Testing with recommendation: {recommendation.course.code} for {recommendation.student.user.get_full_name()}")
    
    # Test 1: View the recommendation review page
    url = reverse('counselor_recommendation_review', args=[recommendation.id])
    response = client.get(url)
    
    if response.status_code == 200:
        print("✓ Recommendation review page loads successfully")
    else:
        print(f"❌ Recommendation review page failed: {response.status_code}")
        return False
    
    # Test 2: Submit an approval
    approval_data = {
        'status': 'approved',
        'counselor_notes': 'This is an excellent course recommendation for the student based on their academic performance and career goals.',
    }
    
    response = client.post(url, approval_data)
    
    if response.status_code == 302:  # Should redirect after successful submission
        print("✓ Recommendation approval submitted successfully")
        
        # Check if the approval was saved
        approval = RecommendationApproval.objects.filter(
            recommendation=recommendation,
            counselor__user__username='counselor'
        ).first()
        
        if approval and approval.status == 'approved':
            print("✓ Approval status saved correctly")
            print(f"  - Status: {approval.get_status_display()}")
            print(f"  - Notes: {approval.counselor_notes}")
        else:
            print("❌ Approval not saved correctly")
            return False
            
        # Check if feedback was created
        feedback = CounselorFeedback.objects.filter(
            student=recommendation.student,
            counselor__user__username='counselor',
            feedback_type='course_recommendation'
        ).order_by('-created_at').first()
        
        if feedback:
            print("✓ Automatic feedback created for student")
            print(f"  - Title: {feedback.title}")
        else:
            print("❌ Automatic feedback not created")
            
    else:
        print(f"❌ Recommendation approval submission failed: {response.status_code}")
        if hasattr(response, 'content'):
            print(f"   Error content: {response.content[:300]}")
        return False
    
    # Test 3: Test modification workflow
    print("\n--- Testing Modification Workflow ---")
    
    # Get another recommendation for modification test
    other_rec = Recommendation.objects.exclude(id=recommendation.id).first()
    if other_rec:
        url = reverse('counselor_recommendation_review', args=[other_rec.id])
        
        modification_data = {
            'status': 'modified',
            'counselor_notes': 'The recommendation is good but needs some adjustments.',
            'modified_reasoning': 'This course is recommended with the caveat that the student should complete prerequisite review sessions first.'
        }
        
        response = client.post(url, modification_data)
        
        if response.status_code == 302:
            print("✓ Recommendation modification submitted successfully")
            
            # Check the modification
            approval = RecommendationApproval.objects.filter(
                recommendation=other_rec,
                counselor__user__username='counselor'
            ).first()
            
            if approval and approval.status == 'modified':
                print("✓ Modification status saved correctly")
                print(f"  - Modified reasoning: {approval.modified_reasoning}")
            else:
                print("❌ Modification not saved correctly")
        else:
            print(f"❌ Recommendation modification failed: {response.status_code}")
    
    # Test 4: Test rejection workflow
    print("\n--- Testing Rejection Workflow ---")
    
    # Get another recommendation for rejection test
    reject_rec = Recommendation.objects.exclude(
        id__in=[recommendation.id, other_rec.id if other_rec else 0]
    ).first()
    
    if reject_rec:
        url = reverse('counselor_recommendation_review', args=[reject_rec.id])
        
        rejection_data = {
            'status': 'rejected',
            'counselor_notes': 'This course is not suitable for the student at this time due to insufficient prerequisites.',
        }
        
        response = client.post(url, rejection_data)
        
        if response.status_code == 302:
            print("✓ Recommendation rejection submitted successfully")
            
            # Check the rejection
            approval = RecommendationApproval.objects.filter(
                recommendation=reject_rec,
                counselor__user__username='counselor'
            ).first()
            
            if approval and approval.status == 'rejected':
                print("✓ Rejection status saved correctly")
            else:
                print("❌ Rejection not saved correctly")
        else:
            print(f"❌ Recommendation rejection failed: {response.status_code}")
    
    return True

def test_recommendations_list():
    """Test the recommendations list view"""
    print("\n=== Testing Recommendations List ===")
    
    client = Client()
    client.login(username='counselor', password='password123')
    
    # Test the main recommendations page
    url = reverse('counselor_recommendations')
    response = client.get(url)
    
    if response.status_code == 200:
        print("✓ Recommendations list page loads successfully")
        
        # Test filtering
        filter_tests = [
            ('?status=pending', 'Pending filter'),
            ('?status=approved', 'Approved filter'),
            ('?status=modified', 'Modified filter'),
            ('?status=rejected', 'Rejected filter'),
            ('?status=all', 'All filter'),
        ]
        
        for filter_param, description in filter_tests:
            filter_url = url + filter_param
            response = client.get(filter_url)
            
            if response.status_code == 200:
                print(f"✓ {description} works correctly")
            else:
                print(f"❌ {description} failed: {response.status_code}")
                
    else:
        print(f"❌ Recommendations list page failed: {response.status_code}")
        return False
    
    return True

def main():
    """Run all recommendation workflow tests"""
    print("CourseRec Recommendation Workflow Test")
    print("=" * 50)
    
    try:
        success = True
        success &= test_recommendations_list()
        success &= test_recommendation_workflow()
        
        print("\n" + "=" * 50)
        if success:
            print("✅ All recommendation workflow tests passed!")
            print("The recommendation system is working correctly.")
        else:
            print("❌ Some recommendation workflow tests failed!")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
