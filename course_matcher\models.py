from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
import json


class Department(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Course(models.Model):
    DIFFICULTY_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
    ]
    
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE)
    credits = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(6)])
    description = models.TextField()
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False)
    difficulty = models.CharField(max_length=20, choices=DIFFICULTY_CHOICES, default='intermediate')
    topics = models.JSONField(default=list, help_text="List of course topics/keywords for content-based matching")
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class StudentProfile(models.Model):
    YEAR_CHOICES = [
        ('freshman', 'Freshman'),
        ('sophomore', 'Sophomore'),
        ('junior', 'Junior'),
        ('senior', 'Senior'),
        ('graduate', 'Graduate'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    student_id = models.CharField(max_length=20, unique=True)
    year = models.CharField(max_length=20, choices=YEAR_CHOICES)
    major = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    interests = models.JSONField(default=list, help_text="List of academic interests/topics")
    career_goals = models.TextField(blank=True)
    preferred_difficulty = models.CharField(max_length=20, choices=Course.DIFFICULTY_CHOICES, default='intermediate')
    date_of_birth = models.DateField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    expected_graduation_year = models.IntegerField(null=True, blank=True, validators=[MinValueValidator(2020), MaxValueValidator(2040)])
    
    def __str__(self):
        return f"{self.user.get_full_name()} ({self.student_id})"
    
    @property
    def gpa(self):
        records = self.academicrecord_set.exclude(grade__isnull=True)
        if not records:
            return 0.0
        
        grade_points = {'A': 4.0, 'A-': 3.7, 'B+': 3.3, 'B': 3.0, 'B-': 2.7, 
                       'C+': 2.3, 'C': 2.0, 'C-': 1.7, 'D+': 1.3, 'D': 1.0, 'F': 0.0}
        
        total_points = 0
        total_credits = 0
        for record in records:
            if record.grade in grade_points:
                points = grade_points[record.grade] * record.course.credits
                total_points += points
                total_credits += record.course.credits
        
        return round(total_points / total_credits, 2) if total_credits > 0 else 0.0
    
    @property
    def total_credits(self):
        return sum(record.course.credits for record in self.academicrecord_set.filter(grade__isnull=False))


class AcademicRecord(models.Model):
    GRADE_CHOICES = [
        ('A', 'A'), ('A-', 'A-'), ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'),
        ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'), ('D+', 'D+'), ('D', 'D'), ('F', 'F'),
    ]
    
    SEMESTER_CHOICES = [
        ('fall', 'Fall'),
        ('spring', 'Spring'),
        ('summer', 'Summer'),
    ]
    
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    semester = models.CharField(max_length=20, choices=SEMESTER_CHOICES)
    year = models.IntegerField()
    grade = models.CharField(max_length=2, choices=GRADE_CHOICES, null=True, blank=True)
    date_enrolled = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['student', 'course']
    
    def __str__(self):
        return f"{self.student.user.get_full_name()} - {self.course.code} ({self.grade or 'In Progress'})"


class Recommendation(models.Model):
    RECOMMENDATION_TYPES = [
        ('classification', 'Classification-based'),
        ('knowledge', 'Knowledge-based'),
        ('content', 'Content-based'),
        ('hybrid', 'Hybrid'),
    ]
    
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    confidence_score = models.FloatField(validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    recommendation_type = models.CharField(max_length=20, choices=RECOMMENDATION_TYPES)
    reasoning = models.TextField(help_text="Explanation for why this course was recommended")
    created_at = models.DateTimeField(auto_now_add=True)
    is_dismissed = models.BooleanField(default=False)
    
    class Meta:
        unique_together = ['student', 'course']
        ordering = ['-confidence_score', '-created_at']
    
    def __str__(self):
        return f"{self.student.user.get_full_name()} -> {self.course.code} ({self.confidence_score:.2f})"


class AdvisingSession(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    advisor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='advising_sessions')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, null=True, blank=True)
    date = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.student.user.get_full_name()} with {self.advisor.get_full_name()} - {self.date.strftime('%Y-%m-%d')}"


class AdmissionTest(models.Model):
    """Model for admission test questions and configuration"""
    QUESTION_TYPES = [
        ('multiple_choice', 'Multiple Choice'),
        ('true_false', 'True/False'),
        ('short_answer', 'Short Answer'),
    ]

    SUBJECT_CHOICES = [
        ('math', 'Mathematics'),
        ('science', 'Science'),
        ('english', 'English'),
        ('history', 'History'),
        ('general_knowledge', 'General Knowledge'),
    ]

    # Keep SUBJECT_AREAS for backward compatibility
    SUBJECT_AREAS = SUBJECT_CHOICES

    DIFFICULTY_CHOICES = [
        ('easy', 'Easy'),
        ('medium', 'Medium'),
        ('hard', 'Hard'),
    ]

    question_text = models.TextField()
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    subject_area = models.CharField(max_length=30, choices=SUBJECT_CHOICES)
    difficulty_level = models.CharField(max_length=20, choices=DIFFICULTY_CHOICES, default='medium')
    options = models.JSONField(default=list, help_text="List of answer options for multiple choice questions")
    correct_answer = models.TextField()
    explanation = models.TextField(blank=True, help_text="Explanation for the correct answer")
    points = models.IntegerField(default=1, validators=[MinValueValidator(1), MaxValueValidator(10)])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    @property
    def answer_options(self):
        """Alias for options field for backward compatibility"""
        return self.options

    @answer_options.setter
    def answer_options(self, value):
        """Setter for answer_options property"""
        self.options = value

    def __str__(self):
        return f"{self.subject_area} - {self.question_text[:50]}..."

    class Meta:
        ordering = ['subject_area', 'difficulty_level', 'created_at']


class AdmissionTestAttempt(models.Model):
    """Model to track student admission test attempts"""
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    total_score = models.IntegerField(default=0)
    max_possible_score = models.IntegerField(default=0)
    percentage_score = models.FloatField(default=0.0)
    is_completed = models.BooleanField(default=False)
    time_taken_minutes = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.student} - {self.percentage_score:.1f}% ({self.started_at.date()})"

    def calculate_score(self):
        """Calculate the total score and percentage"""
        answers = self.admissiontestanswer_set.all()
        total_score = sum(answer.points_earned for answer in answers)
        max_score = sum(answer.question.points for answer in answers)

        self.total_score = total_score
        self.max_possible_score = max_score
        self.percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        self.save()

        return self.percentage_score

    class Meta:
        ordering = ['-started_at']


class AdmissionTestAnswer(models.Model):
    """Model to store student answers to admission test questions"""
    attempt = models.ForeignKey(AdmissionTestAttempt, on_delete=models.CASCADE)
    question = models.ForeignKey(AdmissionTest, on_delete=models.CASCADE)
    student_answer = models.TextField()
    is_correct = models.BooleanField(default=False)
    points_earned = models.IntegerField(default=0)
    answered_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        """Auto-calculate if answer is correct and points earned"""
        if self.question.question_type in ['multiple_choice', 'true_false']:
            self.is_correct = self.student_answer.strip().lower() == self.question.correct_answer.strip().lower()
        else:
            # For short answer questions, manual grading might be needed
            # For now, we'll do a simple string comparison
            self.is_correct = self.student_answer.strip().lower() in self.question.correct_answer.strip().lower()

        self.points_earned = self.question.points if self.is_correct else 0
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.attempt.student} - Q{self.question.id} - {'✓' if self.is_correct else '✗'}"

    class Meta:
        unique_together = ['attempt', 'question']
        ordering = ['answered_at']


class StudentSurvey(models.Model):
    """Model for student learning preferences and style survey"""
    LEARNING_STYLES = [
        ('visual', 'Visual Learner'),
        ('auditory', 'Auditory Learner'),
        ('kinesthetic', 'Kinesthetic Learner'),
        ('reading_writing', 'Reading/Writing Learner'),
    ]

    STUDY_PREFERENCES = [
        ('individual', 'Individual Study'),
        ('group', 'Group Study'),
        ('mixed', 'Mixed Approach'),
    ]

    TIME_PREFERENCES = [
        ('morning', 'Morning Person'),
        ('afternoon', 'Afternoon Person'),
        ('evening', 'Evening Person'),
        ('night', 'Night Owl'),
    ]

    student = models.OneToOneField(StudentProfile, on_delete=models.CASCADE)
    learning_style = models.CharField(max_length=20, choices=LEARNING_STYLES)
    study_preference = models.CharField(max_length=20, choices=STUDY_PREFERENCES)
    time_preference = models.CharField(max_length=20, choices=TIME_PREFERENCES)
    motivation_factors = models.JSONField(default=list, help_text="List of motivation factors")
    preferred_course_format = models.CharField(max_length=20, choices=[
        ('lecture', 'Traditional Lecture'),
        ('seminar', 'Seminar Style'),
        ('lab', 'Laboratory/Hands-on'),
        ('online', 'Online Learning'),
        ('hybrid', 'Hybrid Format'),
    ], default='lecture')
    stress_level = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text="Stress level on a scale of 1-10"
    )
    extracurricular_time = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(40)],
        help_text="Hours per week for extracurricular activities"
    )
    work_hours = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(40)],
        help_text="Hours per week for part-time work"
    )
    technology_comfort = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text="Comfort level with technology on a scale of 1-10"
    )
    career_certainty = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text="How certain are you about your career goals (1-10)"
    )
    additional_comments = models.TextField(blank=True)
    completed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.student} - Survey ({self.completed_at.date()})"

    class Meta:
        ordering = ['-completed_at']


class GuidanceCounselorProfile(models.Model):
    """Model for guidance counselor profile and permissions"""
    SPECIALIZATION_CHOICES = [
        ('academic', 'Academic Counseling'),
        ('career', 'Career Guidance'),
        ('personal', 'Personal Development'),
        ('general', 'General Counseling'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    specialization = models.CharField(max_length=20, choices=SPECIALIZATION_CHOICES, default='general')
    departments = models.ManyToManyField(Department, blank=True, help_text="Departments this counselor is responsible for")
    phone_number = models.CharField(max_length=20, blank=True)
    office_location = models.CharField(max_length=100, blank=True)
    office_hours = models.TextField(blank=True, help_text="Office hours schedule")
    bio = models.TextField(blank=True, help_text="Professional biography")
    years_experience = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_specialization_display()}"

    @property
    def assigned_students_count(self):
        """Count of students assigned to this counselor"""
        return CounselorStudentAssignment.objects.filter(counselor=self, is_active=True).count()

    class Meta:
        ordering = ['user__last_name', 'user__first_name']


class CounselorStudentAssignment(models.Model):
    """Model to track counselor-student assignments"""
    counselor = models.ForeignKey(GuidanceCounselorProfile, on_delete=models.CASCADE)
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    assigned_date = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, help_text="Assignment notes or special considerations")

    def __str__(self):
        return f"{self.counselor.user.get_full_name()} -> {self.student.user.get_full_name()}"

    class Meta:
        unique_together = ['counselor', 'student']
        ordering = ['-assigned_date']


class CounselorFeedback(models.Model):
    """Model for counselor feedback and recommendations to students"""
    FEEDBACK_TYPES = [
        ('general', 'General Feedback'),
        ('academic', 'Academic Guidance'),
        ('career', 'Career Advice'),
        ('course_recommendation', 'Course Recommendation'),
        ('intervention', 'Academic Intervention'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low Priority'),
        ('medium', 'Medium Priority'),
        ('high', 'High Priority'),
        ('urgent', 'Urgent'),
    ]

    counselor = models.ForeignKey(GuidanceCounselorProfile, on_delete=models.CASCADE)
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE)
    feedback_type = models.CharField(max_length=30, choices=FEEDBACK_TYPES, default='general')
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS, default='medium')
    title = models.CharField(max_length=200)
    content = models.TextField()
    recommendations = models.JSONField(default=list, help_text="List of specific recommendations")
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(null=True, blank=True)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.counselor.user.get_full_name()} -> {self.student.user.get_full_name()}: {self.title}"

    class Meta:
        ordering = ['-created_at']


class RecommendationApproval(models.Model):
    """Model to track counselor approval of ML-generated recommendations"""
    APPROVAL_STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('modified', 'Approved with Modifications'),
        ('rejected', 'Rejected'),
    ]

    recommendation = models.OneToOneField(Recommendation, on_delete=models.CASCADE)
    counselor = models.ForeignKey(GuidanceCounselorProfile, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=APPROVAL_STATUS_CHOICES, default='pending')
    counselor_notes = models.TextField(blank=True, help_text="Counselor's notes on the recommendation")
    modified_reasoning = models.TextField(blank=True, help_text="Modified reasoning if approved with changes")
    reviewed_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.recommendation} - {self.get_status_display()}"

    class Meta:
        ordering = ['-reviewed_at']
